name: Release
on:
  push:
    branches:
      - main
      - main-release
      - rec
      - rec-release

  workflow_dispatch:

permissions:
  contents: read # for checkout

jobs:
  # This job is used to sync the main branch to the main:release branch and the rec branch to the rec:release branch
  sync:
    name: Sync
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to push to the main:release and rec:release branches
      id-token: write # to enable use of OIDC for npm provenance
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/rec'
    steps:
      - name: Generate token
        id: generate_token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ vars.SEMANTIC_RELEASE_WORKER_APP_ID }}
          private_key: ${{ secrets.SEMANTIC_RELEASE_WORKER_PRIVATE_KEY }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ steps.generate_token.outputs.token }}
      - name: Setup Git Config
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Sync Branch
        run: |
          SOURCE_BRANCH="${GITHUB_REF_NAME}"
          TARGET_BRANCH="${GITHUB_REF_NAME}-release"
          # Checkout target branch
          git fetch origin ${TARGET_BRANCH}:${TARGET_BRANCH}
          git checkout ${TARGET_BRANCH}
          # Merge changes from SOURCE_BRANCH to TARGET_BRANCH, favoring 'theirs' for conflicts
          git merge -X theirs origin/${SOURCE_BRANCH}
          # Push changes to TARGET_BRANCH
          git push origin ${TARGET_BRANCH}
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}

  release:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests
      id-token: write # to enable use of OIDC for npm provenance
    if: (github.ref == 'refs/heads/main-release' || github.ref == 'refs/heads/rec-release')
    steps:
      - name: Generate token
        id: generate_token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ vars.SEMANTIC_RELEASE_WORKER_APP_ID }}
          private_key: ${{ secrets.SEMANTIC_RELEASE_WORKER_PRIVATE_KEY }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ steps.generate_token.outputs.token }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
      - name: Install dependencies
        run: npm install
      # - name: Verify the integrity of provenance attestations and registry signatures for installed dependencies
      #   run: npm audit signatures
      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npx semantic-release
