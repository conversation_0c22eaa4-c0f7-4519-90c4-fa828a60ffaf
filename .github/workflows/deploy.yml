name: Deployment

on:
  push:
    branches:
      - rec
      - main

  workflow_dispatch:
    inputs:
      FORCE_DEPLOY_APP:
        description: "Force deploy app"
        required: false
        default: "false"
      FORCE_DEPLOY_CRON:
        description: "Force deploy cron"
        required: false
        default: "false"

env:
  # push => github.event.before
  # pull_request => github.event.pull_request.base.sha
  # workflow_dispatch => HEAD^
  TURBO_RUN_FILTER: ${{ github.event_name == 'pull_request' && github.event.pull_request.base.sha || github.event_name == 'push' && github.event.before || 'HEAD^' }}

jobs:
  checks:
    name: Build
    runs-on: ubuntu-latest
    outputs:
      changed-app: ${{ steps.changed-app.outputs.result }}
      changed-cron: ${{ steps.changed-cron.outputs.result }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
      - name: Changeset
        id: changeset
        # 1. We need the 'output' of a turbo dry-run to get a json with all affected packages of these run.
        # 2. The multi line json string is transformed to a single line string.
        # 3. The single line json string is set to an 'output' (or result) of this step.
        run: |
          content=`npx -y turbo build --filter="...[$TURBO_RUN_FILTER]" --dry-run=json`
          echo 'result<<EOF' >> $GITHUB_OUTPUT
          echo $content >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT
          echo $content > $GITHUB_WORKSPACE/result.json
      - name: Upload Result as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: changeset-result
          path: ${{ github.workspace }}/result.json
      - name: Changed app?
        id: changed-app
        if: ${{ contains(fromJSON(steps.changeset.outputs.result).packages, '@starkhub/app') || github.event.inputs.FORCE_DEPLOY_APP == 'true' }}
        run: |
          echo "result=true" >> $GITHUB_OUTPUT
      - name: Changed cron?
        id: changed-cron
        if: ${{ contains(fromJSON(steps.changeset.outputs.result).packages, '@starkhub/cron') || github.event.inputs.FORCE_DEPLOY_CRON == 'true' }}
        run: |
          echo "result=true" >> $GITHUB_OUTPUT
      - name: Changed landing?
        id: changed-landing
        if: ${{ contains(fromJSON(steps.changeset.outputs.result).packages, '@starkhub/landing') || github.event.inputs.FORCE_DEPLOY_LANDING == 'true' }}
        run: |
          echo "result=true" >> $GITHUB_OUTPUT

  parse_branch_name:
    name: Parse branch name
    runs-on: ubuntu-latest
    outputs:
      branch: ${{ steps.parse_branch_name.outputs.branch }}
    steps:
      - name: Parse branch name
        id: parse_branch_name
        run: |
          echo "branch=$(echo ${GITHUB_REF##*/} | tr -d -)" >> $GITHUB_OUTPUT

  deploy_app:
    name: Deploy app
    runs-on: ubuntu-latest
    needs:
      - checks
      - parse_branch_name
    environment:
      name: ${{ needs.parse_branch_name.outputs.branch }}
    if: ${{ needs.checks.outputs.changed-app == 'true' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Deploy app
        run: |
          echo "### Deploying app 🚀" >> $GITHUB_STEP_SUMMARY
          echo "Found changes in app compared to this commit $TURBO_RUN_FILTER" >> $GITHUB_STEP_SUMMARY
          cp apps/app/Dockerfile ./
          curl https://cli-assets.heroku.com/install.sh | sh
          heroku config -a $HEROKU_APP_NAME__APP -s > apps/app/.env
          heroku stack:set container -a $HEROKU_APP_NAME__APP
          heroku container:login
          heroku container:push web -a $HEROKU_APP_NAME__APP --verbose
          heroku labs:enable --app=$HEROKU_APP_NAME__APP runtime-new-layer-extract
          heroku container:release web -a $HEROKU_APP_NAME__APP
        env:
          HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
          HEROKU_APP_NAME__APP: ${{ vars.HEROKU_APP_NAME__APP }}

  deploy_cron:
    name: Deploy cron
    runs-on: ubuntu-latest
    needs:
      - checks
      - parse_branch_name
    environment:
      name: ${{ needs.parse_branch_name.outputs.branch }}
    if: ${{ needs.checks.outputs.changed-cron == 'true' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Deploy cron
        run: |
          echo "### Deploying cron 🚀" >> $GITHUB_STEP_SUMMARY
          echo "Found changes in cron compared to this commit $TURBO_RUN_FILTER" >> $GITHUB_STEP_SUMMARY
          cp apps/cron/Dockerfile ./
          curl https://cli-assets.heroku.com/install.sh | sh
          heroku config -a $HEROKU_APP_NAME__CRON -s > apps/cron/.env
          heroku stack:set container -a $HEROKU_APP_NAME__CRON
          heroku container:login
          heroku container:push worker -a $HEROKU_APP_NAME__CRON --verbose
          heroku labs:enable --app=$HEROKU_APP_NAME__CRON runtime-new-layer-extract
          heroku container:release worker -a $HEROKU_APP_NAME__CRON
        env:
          HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
          HEROKU_APP_NAME__CRON: ${{ vars.HEROKU_APP_NAME__CRON }}

  deploy_landing:
    name: Deploy landing
    runs-on: ubuntu-latest
    needs:
      - checks
      - parse_branch_name
    environment:
      name: ${{ needs.parse_branch_name.outputs.branch }}
    if: ${{ needs.checks.outputs.changed-landing == 'true' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Deploy landing
        run: |
          echo "### Deploying landing 🚀" >> $GITHUB_STEP_SUMMARY
          echo "Found changes in landing compared to this commit $TURBO_RUN_FILTER" >> $GITHUB_STEP_SUMMARY
          cp apps/landing/Dockerfile ./
          curl https://cli-assets.heroku.com/install.sh | sh
          heroku config -a $HEROKU_APP_NAME__LANDING -s > apps/landing/.env
          heroku stack:set container -a $HEROKU_APP_NAME__LANDING
          heroku container:login
          heroku container:push web -a $HEROKU_APP_NAME__LANDING --verbose
          heroku labs:enable --app=$HEROKU_APP_NAME__LANDING runtime-new-layer-extract
          heroku container:release web -a $HEROKU_APP_NAME__LANDING
        env:
          HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
          HEROKU_APP_NAME__LANDING: ${{ vars.HEROKU_APP_NAME__LANDING }}
