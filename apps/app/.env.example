REACT_EDITOR=code
NEXT_TELEMETRY_DISABLED=1
ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
DATABASE_PRISMA_URL="postgresql://postgres:postgres@localhost:5432/postgres?schema=public"
DATABASE_URL_NON_POOLING="postgresql://postgres:postgres@localhost:5432/postgres?schema=public"
PASSWORD_HASHER_SECRET=secret
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=secret
GITHUB_CLIENT_ID=secret
GITHUB_CLIENT_SECRET=secret
AUTH_ADMIN_EMAIL=<EMAIL>
AUTH_ADMIN_PASSWORD=Azerty123!
NEXT_PUBLIC_IS_DEMO=true
NEXT_PUBLIC_DEMO_EMAIL=<EMAIL>
NEXT_PUBLIC_DEMO_PASSWORD=Azerty123!
REDIS_HOST=localhost
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_PORT=6379
REDIS_USE_TLS=false
SMTP_HOST=youtSmptHost
SMTP_PORT=465
SMTP_USERNAME=secret
SMTP_PASSWORD=secret
SMTP_FROM_NAME=FromName
SMTP_FROM_EMAIL=<EMAIL>
NEXT_PUBLIC_ENABLE_MAILING_SERVICE=false
SUPPORT_EMAIL=<EMAIL>
S3_REGION=fr-par
NEXT_PUBLIC_S3_BUCKET_NAME=secret
S3_ACCESS_KEY_ID=secret
S3_SECRET_ACCESS_KEY=secret
NEXT_PUBLIC_S3_ENDPOINT=s3.fr-par.scw.cloud
ENABLE_S3_SERVICE=false
DISABLE_REGISTRATION=false
OPENAI_API_KEY=<YOUR_KEY>
GOOGLE_GENERATIVE_AI_API_KEY=<YOUR_KEY>
MANGOPAY_PROXY_URL=https://freelance-and-me-backend-api-02256833d715.herokuapp.com
MANGOPAY_PROXY_AUTH_KEY=secret