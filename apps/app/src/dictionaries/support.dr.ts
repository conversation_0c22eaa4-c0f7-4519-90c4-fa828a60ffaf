export const SupportDr = {
  client: {
    title: "Support Client",
    newTicket: "Nouveau ticket",
    myTickets: "Mes tickets",
    noTickets: "Vous n'avez pas encore de tickets",
    createTicket: "Créer un ticket",
    ticketTitle: "Titre du ticket",
    message: "Message",
    attachment: "Pièce jointe (optionnelle)",
    send: "Envoyer",
    reply: "Répondre",
    addReply: "Ajouter une réponse",
    back: "Retour à mes tickets",
    ticketCreated: "Votre ticket a été créé avec succès",
    messageSent: "Votre message a été envoyé",
  },
  admin: {
    title: "Gestion des tickets",
    ticketList: "Liste des tickets",
    noTickets: "Aucun ticket trouvé",
    filterBy: "Filtrer par statut",
    all: "Tous",
    open: "Ouverts",
    inProgress: "En cours",
    completed: "Résolus",
    updateStatus: "Mettre à jour le statut",
    markAsInProgress: "Marquer comme en cours",
    markAsCompleted: "Marquer comme résolu",
    ticketDetails: "Détails du ticket",
    clientInfo: "Information client",
    back: "Retour à la liste",
    statusUpdated: "Statut mis à jour avec succès",
  },
  common: {
    status: {
      OPEN: "Ouvert",
      IN_PROGRESS: "En cours",
      COMPLETED: "Résolu",
    },
    sendingMessage: "Envoi en cours...",
    loadingTickets: "Chargement des tickets...",
    from: "De",
    date: "Date",
    actions: "Actions",
    view: "Voir",
    attachFile: "Joindre un fichier",
    removeFile: "Supprimer le fichier",
  },
  errors: {
    titleRequired: "Le titre est requis",
    messageRequired: "Le message est requis",
    fileTooLarge: "Le fichier est trop volumineux",
    uploadFailed: "L'envoi du fichier a échoué",
    unknownError: "Une erreur est survenue",
  },
} as const
