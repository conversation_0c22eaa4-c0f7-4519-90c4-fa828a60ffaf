import { z } from "zod"

import { prisma } from "../../lib/prisma"
import { publicProcedure, router } from "../../lib/server/trpc"

export const promptRouter = router({
  create: publicProcedure
    .input(
      z.object({
        title: z.string(),
        body: z.string(),
        agentId: z.number().optional(),
        categoryId: z.number().optional(),
      })
    )
    .mutation(async ({ input }) => {
      return await prisma.prompt.create({
        data: {
          title: input.title,
          body: input.body,
          agent: input.agentId ? { connect: { id: input.agentId } } : undefined,
          category: input.categoryId ? { connect: { id: input.categoryId } } : undefined,
        },
      })
    }),

  update: publicProcedure
    .input(
      z.object({
        id: z.number(),
        title: z.string().optional(),
        body: z.string().optional(),
        agentId: z.number().optional(),
        categoryId: z.number().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { id, ...data } = input
      return await prisma.prompt.update({
        where: { id },
        data: {
          title: data.title,
          body: data.body,
          agent:
            data.agentId !== undefined
              ? data.agentId === null
                ? { disconnect: true }
                : { connect: { id: data.agentId } }
              : undefined,
          category:
            data.categoryId !== undefined
              ? data.categoryId === null
                ? { disconnect: true }
                : { connect: { id: data.categoryId } }
              : undefined,
        },
      })
    }),

  getAll: publicProcedure.query(async () => {
    return await prisma.prompt.findMany({
      include: {
        category: true,
      },
    })
  }),

  getById: publicProcedure.input(z.number()).query(async ({ input }) => {
    return await prisma.prompt.findUnique({
      where: { id: input },
      include: {
        category: true,
      },
    })
  }),

  delete: publicProcedure.input(z.number()).mutation(async ({ input }) => {
    return await prisma.prompt.delete({
      where: { id: input },
    })
  }),
})
