import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { modoAuthenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"

const featureSchema = z.object({
  text: z.string(),
  included: z.boolean(),
})

const restrictionSchema = z.object({
  type: z.enum(["MAX_MESSAGES_PER_CHAT", "MAX_SAVED_CHATS", "MAX_AGENTS", "MAX_CATEGORIES"]),
  value: z.number().nullable(),
})

const basePlanSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  monthlyPrice: z.number().positive(),
  annualPrice: z.number().positive(),
  monthlyRefundPercentage: z.number().int().min(0).max(100).optional(),
  annualRefundPercentage: z.number().int().min(0).max(100).optional(),
  features: z.array(featureSchema),
  restrictions: z.array(restrictionSchema),
  isRecommended: z.boolean().default(false),
  isActive: z.boolean().default(true),
})

const planSchema = basePlanSchema

// Types pour les plans
export type Feature = {
  text: string
  included: boolean
}

export type PlanRestriction = {
  type: "MAX_MESSAGES_PER_CHAT" | "MAX_SAVED_CHATS" | "MAX_AGENTS" | "MAX_CATEGORIES"
  value: number | null // null signifie sans limite
}

export type PlanWithParsedFeatures = {
  id: number
  name: string
  description: string | null
  monthlyPrice: number
  annualPrice: number
  monthlyRefundPercentage: number | null
  annualRefundPercentage: number | null
  features: Feature[]
  restrictions: PlanRestriction[]
  isRecommended: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  _count: {
    subscriptions: number
  }
}

export const planRouter = router({
  create: modoAuthenticatedProcedure.input(planSchema).mutation(async ({ input }) => {
    const { features, restrictions, monthlyRefundPercentage, annualRefundPercentage, ...rest } = input

    // Convertir les features en strings JSON pour le stockage
    const featuresAsStrings = features.map((feature) => JSON.stringify(feature))

    // Créer le plan
    const plan = await prisma.plan.create({
      data: {
        ...rest,
        monthlyRefundPercentage: monthlyRefundPercentage || null,
        annualRefundPercentage: annualRefundPercentage || null,
        features: featuresAsStrings,
      },
    })

    // Créer les restrictions associées
    if (restrictions && restrictions.length > 0) {
      await prisma.planRestriction.createMany({
        data: restrictions.map((restriction) => ({
          planId: plan.id,
          type: restriction.type,
          value: restriction.value,
        })),
      })
    }

    return plan
  }),

  update: modoAuthenticatedProcedure
    .input(z.object({ id: z.number(), ...basePlanSchema.shape }))
    .mutation(async ({ input }) => {
      const { id, features, restrictions, monthlyRefundPercentage, annualRefundPercentage, ...rest } = input

      // Convertir les features en strings JSON pour le stockage
      const featuresAsStrings = features.map((feature) => JSON.stringify(feature))

      // Mettre à jour le plan
      const plan = await prisma.plan.update({
        where: { id },
        data: {
          ...rest,
          monthlyRefundPercentage: monthlyRefundPercentage || null,
          annualRefundPercentage: annualRefundPercentage || null,
          features: featuresAsStrings,
        },
      })

      // Supprimer les anciennes restrictions
      await prisma.planRestriction.deleteMany({
        where: { planId: id },
      })

      // Créer les nouvelles restrictions
      if (restrictions && restrictions.length > 0) {
        await prisma.planRestriction.createMany({
          data: restrictions.map((restriction) => ({
            planId: id,
            type: restriction.type,
            value: restriction.value,
          })),
        })
      }

      return plan
    }),

  delete: modoAuthenticatedProcedure.input(z.number()).mutation(async ({ input }) => {
    return await prisma.plan.delete({ where: { id: input } })
  }),

  getById: publicProcedure.input(z.number()).query(async ({ input }) => {
    const plan = await prisma.plan.findUnique({
      where: { id: input },
      include: {
        restrictions: true,
      },
    })

    if (!plan) return null

    // Convertir les strings JSON en objets features
    const parsedFeatures = plan.features.map((feature) => {
      try {
        return JSON.parse(feature)
      } catch {
        // Fallback pour la rétrocompatibilité
        return { text: feature, included: true }
      }
    })

    // Convertir les restrictions de la base de données au format attendu
    const formattedRestrictions = plan.restrictions.map((restriction) => ({
      type: restriction.type as PlanRestriction["type"],
      value: restriction.value,
    }))

    return {
      ...plan,
      features: parsedFeatures,
      restrictions: formattedRestrictions,
    } as PlanWithParsedFeatures
  }),

  getAll: publicProcedure.query(async () => {
    const plans = await prisma.plan.findMany({
      where: { isActive: true },
      orderBy: { monthlyPrice: "asc" },
      include: {
        restrictions: true,
      },
    })

    // Convertir les strings JSON en objets features pour chaque plan
    return plans.map((plan) => {
      const parsedFeatures = plan.features.map((feature) => {
        try {
          return JSON.parse(feature)
        } catch {
          // Fallback pour la rétrocompatibilité
          return { text: feature, included: true }
        }
      }) as Feature[]

      // Convertir les restrictions de la base de données au format attendu
      const formattedRestrictions = plan.restrictions.map((restriction) => ({
        type: restriction.type as PlanRestriction["type"],
        value: restriction.value,
      }))

      return {
        ...plan,
        features: parsedFeatures,
        restrictions: formattedRestrictions,
      }
    })
  }),

  getAllForAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(50).default(15),
      })
    )
    .query(async ({ input }) => {
      const { page, pageSize } = input
      const skip = (page - 1) * pageSize

      const plans = await prisma.plan.findMany({
        skip,
        take: pageSize,
        include: {
          restrictions: true,
          _count: {
            select: { subscriptions: true },
          },
        },
        orderBy: { monthlyPrice: "asc" },
      })

      const totalCount = await prisma.plan.count()

      // Parse features and format restrictions
      const parsedPlans = plans.map((plan) => {
        const parsedFeatures = plan.features.map((feature) => {
          try {
            return JSON.parse(feature)
          } catch {
            return { text: feature, included: true }
          }
        }) as Feature[]

        const formattedRestrictions = plan.restrictions.map((restriction) => ({
          type: restriction.type as PlanRestriction["type"],
          value: restriction.value,
        }))

        return {
          ...plan,
          features: parsedFeatures,
          restrictions: formattedRestrictions,
        }
      })

      return {
        data: parsedPlans,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        },
      }
    }),
})
