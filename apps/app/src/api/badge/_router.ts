import { z } from "zod"

import { DAYS_BEFORE_CATEGORY_CHANGE } from "@/constants/restrictions"
import { getUserRestrictionValue } from "@/lib/plan"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, iaBuilderAuthenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"
import { logger } from "@coheadcoaching/lib"

const badgeSchema = z.object({
  title: z.string(),
})

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(50).default(6),
})

export const badgeRouter = router({
  create: authenticatedProcedure.input(badgeSchema).mutation(async ({ input }) => {
    return await prisma.badge.create({ data: input })
  }),

  update: authenticatedProcedure.input(z.object({ id: z.number(), title: z.string() })).mutation(async ({ input }) => {
    return await prisma.badge.update({ where: { id: input.id }, data: { title: input.title } })
  }),

  delete: authenticatedProcedure.input(z.number()).mutation(async ({ input }) => {
    return await prisma.badge.delete({ where: { id: input } })
  }),

  getById: publicProcedure.input(z.number()).query(async ({ input }) => {
    return await prisma.badge.findUnique({ where: { id: input } })
  }),

  getAll: publicProcedure.query(async () => {
    return await prisma.badge.findMany()
  }),

  // Pour les utilisateurs non connectés - retourne tous les badges
  getForFilter: publicProcedure.query(async () => {
    // Récupérer tous les badges avec au moins un agent
    return await prisma.badge.findMany({
      where: {
        agents: {
          some: {},
        },
      },
      orderBy: {
        title: "asc",
      },
    })
  }),

  // Pour les utilisateurs connectés - filtre les badges selon les restrictions du plan
  getForFilterAuthenticated: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user?.id as string

    // Récupérer tous les badges avec au moins un agent
    const allBadges = await prisma.badge.findMany({
      where: {
        agents: {
          some: {},
        },
      },
      orderBy: {
        title: "asc",
      },
    })

    try {
      // Vérifier si l'utilisateur a un plan avec des catégories illimitées
      const maxCategories = await getUserRestrictionValue(userId, "MAX_CATEGORIES")
      logger.warn("MAX_CAT", maxCategories)
      const hasUnlimitedCategories = maxCategories === null

      // Si l'utilisateur a des catégories illimitées, retourner tous les badges
      if (hasUnlimitedCategories) {
        return allBadges
      }

      // Récupérer les catégories (badges) sélectionnées par l'utilisateur
      const userSelection = await prisma.userCategorySelection.findUnique({
        where: { userId },
      })
      const userCategoryIds = userSelection?.categoryIds || []
      logger.warn("USER_CAT", userCategoryIds)

      // Si l'utilisateur n'a pas sélectionné de catégories et n'a pas de catégories illimitées,
      // retourner une liste vide pour forcer la sélection
      if (userCategoryIds.length === 0) {
        return []
      }

      // Si l'utilisateur a sélectionné des catégories et a un abonnement limité,
      // retourner uniquement les badges correspondant à ses catégories sélectionnées
      // pour respecter les restrictions de son abonnement
      const filteredBadges = allBadges.filter((badge) => userCategoryIds.includes(badge.id))
      logger.log(filteredBadges)

      return filteredBadges
    } catch (error) {
      console.error("Error fetching badges for filter:", error)
      return allBadges
    }
  }),

  getAllForAdmin: iaBuilderAuthenticatedProcedure.input(paginationSchema).query(async ({ input }) => {
    const { page, pageSize } = input
    const skip = (page - 1) * pageSize

    const badges = await prisma.badge.findMany({
      skip,
      take: pageSize,
      include: {
        _count: {
          select: { agents: true },
        },
      },
    })

    const totalCount = await prisma.badge.count()

    return {
      data: badges,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }),

  getActiveWithAgents: publicProcedure.query(async () => {
    // Récupérer tous les badges qui ont au moins un agent associé
    const badgesWithAgents = await prisma.badge.findMany({
      where: {
        agents: {
          some: {}, // Au moins un agent associé
        },
      },
      orderBy: {
        title: "asc",
      },
    })

    return badgesWithAgents
  }),

  updateUserCategorySelections: authenticatedProcedure
    .input(
      z.object({
        categoryIds: z.array(z.number()),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.session?.user.id

      try {
        // Vérifier la limite de catégories selon le plan de l'utilisateur
        const maxCategories = await getUserRestrictionValue(userId, "MAX_CATEGORIES")

        // Si l'utilisateur a une limite de catégories et essaie d'en sélectionner plus
        if (maxCategories !== null && input.categoryIds.length > maxCategories) {
          return {
            success: false,
            message: `Vous ne pouvez sélectionner que ${maxCategories} catégories maximum avec votre plan actuel.`,
          }
        }

        // Vérifier si l'utilisateur a déjà fait une sélection
        const existingSelection = await prisma.userCategorySelection.findUnique({
          where: { userId },
        })

        if (existingSelection) {
          // Vérifier si la dernière mise à jour date de moins de DAYS_BEFORE_CATEGORY_CHANGE jours
          const lastUpdate = new Date(existingSelection.lastUpdated)
          const now = new Date()
          const daysSinceLastUpdate = Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24))

          // Récupérer l'historique des plans de l'utilisateur pour détecter un changement de plan
          const userSubscriptions = await prisma.subscription.findMany({
            where: { userId },
            orderBy: { createdAt: "desc" },
            take: 2,
            include: {
              plan: {
                include: {
                  restrictions: true,
                },
              },
            },
          })

          // Vérifier si l'utilisateur a changé de plan récemment
          const hasPlanChanged = userSubscriptions.length > 1

          // Ne pas appliquer la restriction de DAYS_BEFORE_CATEGORY_CHANGE jours si l'utilisateur a changé de plan
          // ou s'il réduit le nombre de catégories
          const isReducingCategories = input.categoryIds.length < existingSelection.categoryIds.length
          const needsAdjustmentDueToNewPlan =
            hasPlanChanged || (maxCategories !== null && existingSelection.categoryIds.length > maxCategories)

          // Si l'utilisateur doit ajuster ses catégories en raison d'un changement de plan,
          // nous ne voulons pas appliquer la restriction de DAYS_BEFORE_CATEGORY_CHANGE jours
          if (
            daysSinceLastUpdate < DAYS_BEFORE_CATEGORY_CHANGE &&
            !isReducingCategories &&
            !needsAdjustmentDueToNewPlan
          ) {
            return {
              success: false,
              message: `Vous ne pouvez modifier vos catégories qu'une fois tous les ${DAYS_BEFORE_CATEGORY_CHANGE} jours. Prochain changement possible dans ${DAYS_BEFORE_CATEGORY_CHANGE - daysSinceLastUpdate} jours.`,
            }
          }

          // Mettre à jour la sélection
          await prisma.userCategorySelection.update({
            where: { userId },
            data: {
              categoryIds: input.categoryIds,
              // Toujours mettre à jour la date pour commencer un nouveau délai de DAYS_BEFORE_CATEGORY_CHANGE jours
              lastUpdated: new Date(),
            },
          })
        } else {
          // Créer une nouvelle sélection
          await prisma.userCategorySelection.create({
            data: {
              userId,
              categoryIds: input.categoryIds,
              lastUpdated: new Date(),
            },
          })
        }

        return { success: true }
      } catch (error) {
        console.error("Error updating user category selections:", error)
        return { success: false, message: "Une erreur est survenue lors de la mise à jour de vos catégories." }
      }
    }),

  getUserCategorySelections: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id

    try {
      const userSelection = await prisma.userCategorySelection.findUnique({
        where: { userId },
      })

      return {
        categoryIds: userSelection?.categoryIds || [],
        lastUpdated: userSelection?.lastUpdated || null,
      }
    } catch (error) {
      console.error("Error fetching user category selections:", error)
      return { categoryIds: [], lastUpdated: null }
    }
  }),
})
