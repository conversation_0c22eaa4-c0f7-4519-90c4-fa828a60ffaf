import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, router, savAuthenticatedProcedure } from "@/lib/server/trpc"
import { TicketStatus } from "@/types/support"
import { logger } from "@coheadcoaching/lib"
import { TRPCError } from "@trpc/server"

const ticketSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  content: z.string().min(1, "Le contenu du message est requis"),
  attachmentId: z.string().optional(),
})

const statusSchema = z.nativeEnum(TicketStatus)

export const ticketRouter = router({
  create: authenticatedProcedure.input(ticketSchema).mutation(async ({ input, ctx }) => {
    const { title, content, attachmentId } = input
    const userId = ctx.session?.user.id

    try {
      // Si un attachmentId est fourni (qui est en fait une clé S3)
      let fileId: string | undefined = undefined

      if (attachmentId) {
        // Vérifier si un fichier avec cette clé existe déjà
        const existingFile = await prisma.file.findFirst({
          where: { key: attachmentId },
        })

        if (existingFile) {
          fileId = existingFile.id
        } else {
          // Vérifier si un FileUploading avec cette clé existe
          const fileUploading = await prisma.fileUploading.findFirst({
            where: { key: attachmentId },
          })

          if (!fileUploading) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Le fichier joint n'existe pas",
            })
          }

          // Créer une entrée File à partir du FileUploading
          const newFile = await prisma.file.create({
            data: {
              key: fileUploading.key,
              filetype: fileUploading.filetype,
              bucket: fileUploading.bucket,
              endpoint: fileUploading.endpoint,
              fileUploadingId: fileUploading.id,
            },
          })

          fileId = newFile.id
        }
      }

      // Créer le ticket avec le fileId
      return await prisma.supportTicket.create({
        data: {
          title,
          userId,
          messages: {
            create: {
              content,
              senderId: userId,
              attachmentId: fileId, // Utiliser l'ID du fichier, pas la clé S3
            },
          },
        },
        include: {
          messages: true,
        },
      })
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error
      }

      logger.error("Error creating support ticket", { error, userId })
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Impossible de créer le ticket de support",
      })
    }
  }),

  getAllForAdmin: savAuthenticatedProcedure
    .input(
      z
        .object({
          status: statusSchema.optional(),
        })
        .optional()
    )
    .query(async ({ input }) => {
      const tickets = await prisma.supportTicket.findMany({
        where: input?.status ? { status: input.status } : undefined,
        orderBy: { updatedAt: "desc" },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          messages: {
            take: 1,
            orderBy: { createdAt: "desc" },
          },
          _count: {
            select: { messages: true },
          },
        },
      })
      const opened = await prisma.supportTicket.count({
        where: { status: TicketStatus.OPEN },
      })
      const inProgress = await prisma.supportTicket.count({
        where: { status: TicketStatus.IN_PROGRESS },
      })
      const completed = await prisma.supportTicket.count({
        where: { status: TicketStatus.COMPLETED },
      })
      return { tickets, ticketsCount: { opened, inProgress, completed } }
    }),

  getUserTickets: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id
    return await prisma.supportTicket.findMany({
      where: { userId },
      orderBy: { updatedAt: "desc" },
      include: {
        messages: {
          take: 1,
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: { messages: true },
        },
      },
    })
  }),

  getById: authenticatedProcedure.input(z.string()).query(async ({ input, ctx }) => {
    const userId = ctx.session?.user.id
    const isAdmin = ctx.session?.user.role === "ADMIN"

    const ticket = await prisma.supportTicket.findUnique({
      where: { id: input },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                role: true,
                profilePicture: true,
              },
            },
            attachment: true,
          },
          orderBy: { createdAt: "asc" },
        },
      },
    })

    if (!ticket) {
      throw new TRPCError({ code: "NOT_FOUND", message: "Ticket introuvable" })
    }

    if (!isAdmin && ticket.userId !== userId) {
      throw new TRPCError({ code: "FORBIDDEN", message: "Accès non autorisé" })
    }

    return ticket
  }),

  updateStatus: savAuthenticatedProcedure
    .input(
      z.object({
        ticketId: z.string(),
        status: statusSchema,
      })
    )
    .mutation(async ({ input }) => {
      return await prisma.supportTicket.update({
        where: { id: input.ticketId },
        data: { status: input.status },
      })
    }),
})
