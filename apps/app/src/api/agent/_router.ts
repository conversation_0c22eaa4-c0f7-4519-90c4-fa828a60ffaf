import { z } from "zod"

import { getUserRestrictionValue } from "@/lib/plan"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, iaBuilderAuthenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"
import { Prisma } from "@prisma/client"

// Input schema for creation or update
const agentCreateSchema = z.object({
  icon: z.string(),
  title: z.string(),
  description: z.string(),
  model: z.string().optional().default("gpt-4"),
  promptId: z.number().optional(), // Single prompt ID
  skills: z.array(z.number()).optional(), // Array of skill IDs
  temperature: z.number(),
  personality: z.string().optional(),
  additionalInstructions: z.string().optional().default(""),
  badgeId: z.number().optional(),
})

const agentUpdateSchema = z.object({
  id: z.number(),
  icon: z.string().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  model: z.string().optional(),
  promptId: z.number().optional(), // Single prompt ID
  skills: z.array(z.number()).optional(), // Array of skill IDs
  personality: z.string().optional(),
  temperature: z.number().optional(),
  additionalInstructions: z.string().optional(),
  badgeId: z.number().optional(),
})

// Input schema for pagination
const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(50).default(6),
  withBadge: z.boolean().default(false),
})

export const agentRouter = router({
  create: iaBuilderAuthenticatedProcedure.input(agentCreateSchema).mutation(async ({ input }) => {
    // Construct the data object, omitting undefined fields
    const data: Prisma.AgentCreateInput = {
      icon: input.icon,
      title: input.title,
      description: input.description,
      model: input.model,
      personality: input.personality,
      temperature: input.temperature,
      additionalInstructions: input.additionalInstructions || "",
    }

    // Add badgeId if provided
    if (input.badgeId !== undefined) {
      data.badge = { connect: { id: input.badgeId } }
    }

    // Add skills if provided
    if (input.skills && input.skills.length > 0) {
      data.skills = {
        connect: input.skills.map((skillId) => ({ id: skillId })),
      }
    }

    // Add single prompt if provided
    if (input.promptId) {
      data.prompts = {
        connect: [{ id: input.promptId }],
      }
    }

    return await prisma.agent.create({
      data,
    })
  }),

  update: iaBuilderAuthenticatedProcedure.input(agentUpdateSchema).mutation(async ({ input }) => {
    const id = input.id
    if (isNaN(id)) {
      throw new Error("ID must be a valid number for update")
    }

    // Récupérer l'agent existant pour préserver les champs non modifiés
    const existingAgent = await prisma.agent.findUnique({
      where: { id },
      include: {
        skills: true,
        prompts: true,
      },
    })

    if (!existingAgent) {
      throw new Error("Agent not found")
    }

    // Construct the data object, omitting undefined fields
    const data: Prisma.AgentUpdateInput = {}

    // N'ajouter que les champs qui sont explicitement fournis
    if (input.icon !== undefined) data.icon = input.icon
    if (input.title !== undefined) data.title = input.title
    if (input.description !== undefined) data.description = input.description
    if (input.model !== undefined) data.model = input.model
    if (input.personality !== undefined) data.personality = input.personality
    if (input.temperature !== undefined) data.temperature = input.temperature
    if (input.additionalInstructions !== undefined) data.additionalInstructions = input.additionalInstructions

    // Add badgeId only if it is provided
    if (input.badgeId !== undefined) {
      data.badge = { connect: { id: input.badgeId } }
    }

    // Update skills if provided
    if (input.skills) {
      data.skills = {
        set: input.skills.map((skillId) => ({ id: skillId })),
      }
    }

    // Update to single prompt if provided
    if (input.promptId) {
      // First, disconnect all existing prompts
      await prisma.agent.update({
        where: { id },
        data: {
          prompts: {
            set: [], // Clear existing prompts
          },
        },
      })

      // Then connect the new prompt
      data.prompts = {
        connect: [{ id: input.promptId }],
      }
    }

    try {
      const updatedAgent = await prisma.agent.update({
        where: { id },
        data,
      })

      return updatedAgent
    } catch (error) {
      console.error("Failed to update agent:", error)
      throw new Error("Failed to update agent. Please try again.")
    }
  }),

  delete: iaBuilderAuthenticatedProcedure.input(z.string()).mutation(async ({ input }) => {
    const id = Number(input)
    if (isNaN(id)) {
      throw new Error("ID must be a valid number for deletion")
    }

    // Récupérer les prompts associés à l'agent
    const agent = await prisma.agent.findUnique({
      where: { id },
      include: { prompts: true },
    })

    if (!agent) {
      throw new Error("Agent not found")
    }

    // Supprimer les prompts associés à l'agent
    if (agent.prompts.length > 0) {
      await prisma.prompt.deleteMany({
        where: {
          id: {
            in: agent.prompts.map((prompt) => prompt.id),
          },
        },
      })
    }

    // Supprimer l'agent
    return await prisma.agent.delete({
      where: { id },
    })
  }),

  getById: authenticatedProcedure.input(z.number()).query(async ({ input }) => {
    if (isNaN(input)) {
      throw new Error("ID must be a valid number")
    }

    const agent = await prisma.agent.findUnique({
      where: { id: input },
      include: {
        skills: true,
        prompts: true,
      },
    })

    if (!agent) {
      throw new Error("Agent not found")
    }

    return agent
  }),

  getByIdForAdmin: iaBuilderAuthenticatedProcedure.input(z.number()).query(async ({ input }) => {
    if (isNaN(input)) {
      throw new Error("ID must be a valid number")
    }

    const agent = await prisma.agent.findUnique({
      where: { id: input },
      include: {
        badge: true,
        skills: true,
        prompts: true,
        _count: {
          select: {
            chats: true,
          },
        },
      },
    })

    if (!agent) {
      throw new Error("Agent not found")
    }

    return agent
  }),

  // Pour les utilisateurs non connectés - retourne tous les agents
  getAll: publicProcedure.input(paginationSchema).query(async ({ input }) => {
    const { page, pageSize, withBadge } = input
    const skip = (page - 1) * pageSize

    // Construire la requête de base
    const baseQuery = {
      skip,
      take: pageSize,
      select: {
        id: true,
        icon: true,
        title: true,
        description: true,
        badge: withBadge,
      },
    }

    // Retourner tous les agents pour les utilisateurs non connectés
    const agents = await prisma.agent.findMany(baseQuery)
    const totalCount = await prisma.agent.count()

    return {
      data: agents,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
      filteredByUserCategories: false,
      needsCategorySelection: false,
    }
  }),

  // Pour les utilisateurs connectés - filtre les agents selon les restrictions du plan
  getAllAuthenticated: authenticatedProcedure.input(paginationSchema).query(async ({ input, ctx }) => {
    const { page, pageSize, withBadge } = input
    const skip = (page - 1) * pageSize
    const userId = ctx.session?.user?.id as string

    // Construire la requête de base
    const baseQuery = {
      skip,
      take: pageSize,
      select: {
        id: true,
        icon: true,
        title: true,
        description: true,
        badge: withBadge,
      },
    }

    // Récupérer les catégories (badges) sélectionnées par l'utilisateur
    const userSelection = await prisma.userCategorySelection.findUnique({
      where: { userId },
    })
    const userCategoryIds = userSelection?.categoryIds || []

    // Vérifier si l'utilisateur a un plan avec des catégories illimitées
    const maxCategories = await getUserRestrictionValue(userId, "MAX_CATEGORIES")
    const hasUnlimitedCategories = maxCategories === null

    // Si l'utilisateur n'a pas sélectionné de catégories et n'a pas de catégories illimitées,
    // retourner une liste vide pour forcer la sélection
    if (userCategoryIds.length === 0 && !hasUnlimitedCategories) {
      return {
        data: [],
        pagination: {
          page,
          pageSize,
          totalCount: 0,
          totalPages: 0,
        },
        filteredByUserCategories: false,
        needsCategorySelection: true,
      }
    }

    // Si l'utilisateur a des catégories illimitées, retourner tous les agents
    if (hasUnlimitedCategories) {
      const agents = await prisma.agent.findMany(baseQuery)
      const totalCount = await prisma.agent.count()

      return {
        data: agents,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        },
        filteredByUserCategories: false,
        needsCategorySelection: false,
      }
    }

    // Sinon, filtrer les agents par les catégories sélectionnées par l'utilisateur
    const query = {
      ...baseQuery,
      where: {
        badgeId: {
          in: userCategoryIds,
        },
      },
    }

    const agents = await prisma.agent.findMany(query)
    const totalCount = await prisma.agent.count({
      where: {
        badgeId: {
          in: userCategoryIds,
        },
      },
    })

    return {
      data: agents,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
      filteredByUserCategories: true,
    }
  }),

  getAllForAdmin: iaBuilderAuthenticatedProcedure.input(paginationSchema).query(async ({ input }) => {
    const { page, pageSize, withBadge } = input
    const skip = (page - 1) * pageSize

    const agents = await prisma.agent.findMany({
      skip,
      take: pageSize,
      include: {
        badge: withBadge,
      },
    })

    const totalCount = await prisma.agent.count()

    return {
      data: agents,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }),
})
