import { z } from "zod"

import { prisma } from "../../lib/prisma"
import { iaBuilderAuthenticatedProcedure, router } from "../../lib/server/trpc"

const categorySchema = z.object({
  name: z.string(),
})

export const categoryRouter = router({
  create: iaBuilderAuthenticatedProcedure.input(categorySchema).mutation(async ({ input }) => {
    return await prisma.category.create({ data: input })
  }),

  update: iaBuilderAuthenticatedProcedure
    .input(z.object({ id: z.number(), name: z.string() }))
    .mutation(async ({ input }) => {
      return await prisma.category.update({ where: { id: input.id }, data: { name: input.name } })
    }),

  delete: iaBuilderAuthenticatedProcedure.input(z.number()).mutation(async ({ input }) => {
    return await prisma.category.delete({ where: { id: input } })
  }),

  getById: iaBuilderAuthenticatedProcedure.input(z.number()).query(async ({ input }) => {
    return await prisma.category.findUnique({ where: { id: input } })
  }),

  getAll: iaBuilderAuthenticatedProcedure.query(async () => {
    return await prisma.category.findMany()
  }),
})
