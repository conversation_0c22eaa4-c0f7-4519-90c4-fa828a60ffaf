import { randomUUID } from "crypto"
import { z } from "zod"

import { emailVerificationExpiration } from "@/constants"
import { hash } from "@/lib/bcrypt"
import { env } from "@/lib/env"
// import { Locale } from "@/lib/i18n-config"
// import { _getDictionary } from "@/lib/langs"
import { prisma } from "@/lib/prisma"
import { modoAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { ApiError, handleApiError } from "@/lib/utils/server-utils"
import { Prisma } from "@prisma/client"

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(50).default(6),
})

const userUpdateSchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  email: z.string().email().nullable(),
  username: z.string().nullable(),
  role: z.enum(["USER", "ADMIN", "SA<PERSON>", "<PERSON><PERSON><PERSON>", "IA_BUILDER"]),
  roles: z.array(z.enum(["USER", "ADMIN", "SAV", "MODO", "IA_BUILDER"])).optional(),
  hasPassword: z.boolean().optional(),
  lastLocale: z.string().optional(),
  otpVerified: z.boolean().optional(),
})

export const userRouter = router({
  getById: modoAuthenticatedProcedure.input(z.string()).query(async ({ input }) => {
    if (!input) {
      throw new Error("ID must be a valid number")
    }

    const user = await prisma.user.findUnique({
      where: { id: input },
    })

    if (!user) {
      throw new Error("User not found")
    }

    return user
  }),

  getAll: modoAuthenticatedProcedure.input(paginationSchema).query(async ({ input }) => {
    const { page, pageSize } = input
    const skip = (page - 1) * pageSize

    const users = await prisma.user.findMany({
      skip,
      take: pageSize,
      include: {
        profilePicture: true,
      },
    })

    const totalCount = await prisma.user.count()

    return {
      data: users,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }),

  create: modoAuthenticatedProcedure
    .input(
      z.object({
        name: z.string().nullable(),
        email: z.string().email(),
        username: z.string().nullable(),
        role: z.enum(["USER", "ADMIN", "SAV", "MODO", "IA_BUILDER"]),
        roles: z.array(z.enum(["USER", "ADMIN", "SAV", "MODO", "IA_BUILDER"])).min(1, "Au moins un rôle est requis"),
        password: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const { password, ...rest } = input
        const hashedPassword = password ? await hash(password, 12) : undefined

        // Récupération de la locale de l'admin
        const adminUser = ctx.session?.user
        const lastLocale = adminUser?.lastLocale || "fr" // Par défaut 'fr'

        const user = await prisma.user.create({
          data: {
            ...rest,
            email: rest.email.toLowerCase(),
            password: hashedPassword,
            hasPassword: !!hashedPassword,
            lastLocale,
          },
          include: {
            profilePicture: true,
          },
        })

        // Mise en cache de la locale
        // await redis.setex(`lastLocale:${user.id}`, lastLocaleExpirationInSeconds, lastLocale)

        // Envoi de l'email de vérification
        if (env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE === true) {
          const token = randomUUID()
          await prisma.userEmailVerificationToken.create({
            data: {
              identifier: user.id,
              token,
              expires: new Date(Date.now() + emailVerificationExpiration),
            },
          })

          /*const verificationLink = `${env.VERCEL_URL ?? env.NEXT_PUBLIC_BASE_URL}/verify-email/${token}`
          const locale = lastLocale as Locale

          const dictionary = await _getDictionary("transactionals", locale, {
            hey: true,
            verifyEmail: true,
            footer: true,
            thanksForSigninUpCompleteRegistration: true,
            verifyYourEmailAddress: true,
            verifyYourEmailAddressToCompleteYourRegistration: true,
          })

          const element = VerifyEmail({
            verificationLink,
            actionText: dictionary.verifyEmail,
            contentTitle: dictionary.thanksForSigninUpCompleteRegistration,
            footerText: dictionary.footer,
            heyText: dictionary.hey,
            logoUrl,
            name: user.name ?? "",
            previewText: dictionary.verifyYourEmailAddressToCompleteYourRegistration,
            supportEmail: env.SUPPORT_EMAIL ?? "",
            titleText: dictionary.verifyYourEmailAddress,
          })

          const text = render(element, { plainText: true })
          const html = render(element)

          await sendMail({
            to: user.email,
            subject: dictionary.verifyYourEmailAddress,
            text,
            html,
          })*/
        }

        return user
      } catch (error: unknown) {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            const meta = error.meta
            if (!meta) return ApiError("accountAlreadyExists")

            if ((meta.target as Array<string>).includes("email")) {
              return ApiError("email.exist", "CONFLICT")
            } else if ((meta.target as Array<string>).includes("username")) {
              return ApiError("username.exist", "CONFLICT")
            }
          }
        }
        throw handleApiError(error)
      }
    }),

  update: modoAuthenticatedProcedure.input(userUpdateSchema).mutation(async ({ input }) => {
    const { id, ...userData } = input

    // If roles is provided, make sure it includes USER role
    if (userData.roles && !userData.roles.includes("USER")) {
      userData.roles.push("USER")
    }

    // If ADMIN is in roles, set legacy role field to ADMIN, otherwise USER
    if (userData.roles) {
      userData.role = userData.roles.includes("ADMIN") ? "ADMIN" : "USER"
    }

    const user = await prisma.user.update({
      where: { id },
      data: userData,
    })

    return user
  }),

  delete: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input }) => {
    const user = await prisma.user.delete({
      where: { id: input },
    })

    return user
  }),
})
