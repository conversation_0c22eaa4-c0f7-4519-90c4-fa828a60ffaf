import { z } from "zod"

import { desactivateCard, updateRecurringPaymentCard } from "@/lib/mangopay"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, router } from "@/lib/server/trpc"
import { logger } from "@coheadcoaching/lib"
import { TRPCError } from "@trpc/server"

export const cardRouter = router({
  getUserCards: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    let cards
    try {
      cards = await prisma.mangopayCard.findMany({
        where: {
          userId,
          isTemp: false,
          isActive: true,
        },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          last4: true,
          expirationDate: true,
          isDefault: true,
        },
      })
    } catch (error) {
      logger.error("Error fetching user cards", { error, userId })
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Une erreur est survenue lors de la récupération des cartes",
      })
    }
    return cards
  }),

  delete: authenticatedProcedure.input(z.string()).mutation(async ({ input, ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    const card = await prisma.mangopayCard.findUnique({
      where: { id: input },
      select: {
        id: true,
        userId: true,
        mangopayCardId: true,
        isDefault: true,
      },
    })

    if (!card || card.userId !== userId) {
      throw new TRPCError({ code: "NOT_FOUND", message: "Carte non trouvée" })
    }

    if (card.isDefault) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "Vous ne pouvez pas supprimer la carte par défaut" })
    }

    try {
      if (card.mangopayCardId) {
        await desactivateCard(card.mangopayCardId)
      }

      return await prisma.mangopayCard.update({
        where: { id: input },
        data: { isActive: false },
      })
    } catch (error) {
      logger.error("Erreur lors de la désactivation de la carte", { error, cardId: input })
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Impossible de désactiver la carte",
      })
    }
  }),

  setDefault: authenticatedProcedure.input(z.object({ cardId: z.string() })).mutation(async ({ input, ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    const card = await prisma.mangopayCard.findUnique({
      where: { id: input.cardId },
    })

    if (!card || card.userId !== userId) {
      throw new TRPCError({ code: "NOT_FOUND", message: "Carte non trouvée" })
    }

    const activeSubscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: "ACTIVE",
        mangopayRecurringRegistrationId: { not: null },
      },
      select: { mangopayRecurringRegistrationId: true },
    })

    try {
      if (activeSubscription?.mangopayRecurringRegistrationId) {
        await updateRecurringPaymentCard(activeSubscription.mangopayRecurringRegistrationId, card.mangopayCardId!)
      }

      await prisma.mangopayCard.updateMany({
        where: { userId, isActive: true },
        data: { isDefault: false },
      })

      return await prisma.mangopayCard.update({
        where: { id: input.cardId },
        data: { isDefault: true },
      })
    } catch (error) {
      logger.error("Erreur lors de la mise à jour de la carte par défaut", { error, cardId: input.cardId })
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Impossible de définir la carte par défaut",
      })
    }
  }),

  // register: authenticatedProcedure
  //   .input(
  //     z.object({
  //       cardNumber: z.string(),
  //       cardExpirationDate: z.string(),
  //       cardCvx: z.string(),
  //       cardType: z.enum(["CB_VISA_MASTERCARD", "AMEX", "MAESTRO", "BCMC"]),
  //     })
  //   )
  //   .mutation(async ({ input, ctx }) => {
  //     const userId = ctx.session?.user.id

  //     if (!userId) {
  //       throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
  //     }

  //     try {
  //       const result = await registerCard({
  //         cardNumber: input.cardNumber,
  //         cardExpirationDate: input.cardExpirationDate,
  //         cardCvx: input.cardCvx,
  //         cardType: input.cardType,
  //       })

  //       return result
  //     } catch (error) {
  //       throw new TRPCError({
  //         code: "INTERNAL_SERVER_ERROR",
  //         message: error instanceof Error ? error.message : "Erreur lors de l'enregistrement de la carte",
  //       })
  //     }
  //   }),
})
