@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  --toastify-color-progress-light: hsl(var(--nextui-primary));
  --toastify-color-progress-dark: hsl(var(--nextui-primary));
  --toastify-color-progress-success: hsl(var(--nextui-success));
  --toastify-icon-color-success: hsl(var(--nextui-success));
  --toastify-color-progress-warning: hsl(var(--nextui-warning));
  --toastify-icon-color-warning: hsl(var(--nextui-warning));
  --toastify-color-progress-error: hsl(var(--nextui-danger));
  --toastify-icon-color-error: hsl(var(--nextui-danger));
}

@keyframes blink {
  50% {
    fill: transparent;
  }
}
.dot {
  animation: 1s blink infinite;
  fill: currentColor;
}
.dot:nth-child(2) {
  animation-delay: 250ms;
}
.dot:nth-child(3) {
  animation-delay: 500ms;
}

.loader {
  background-color: transparent;

  color: currentColor;
}

/* React Select - Styles globaux */
.react-select__control {
  background-color: hsl(var(--nextui-background)) !important;
  border-color: hsl(var(--nextui-default-200)) !important;
  border-radius: 0.375rem !important;
  box-shadow: none !important;
  min-height: 40px !important;
  padding: 2px !important;
}

.react-select__control:hover {
  border-color: hsl(var(--nextui-default-300)) !important;
}

.react-select__control--is-focused {
  border-color: hsl(var(--nextui-primary)) !important;
  box-shadow: 0 0 0 1px hsl(var(--nextui-primary)) !important;
}

.react-select__menu {
  background-color: hsl(var(--nextui-background)) !important;
  border-radius: 0.375rem !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  z-index: 9999 !important; /* Augmenter le z-index pour s'assurer qu'il apparaît au-dessus des autres éléments */
  position: absolute !important;
  width: 100% !important;
  margin-top: 4px !important;
}

.react-select__menu-list {
  max-height: 300px !important; /* Hauteur maximale pour éviter qu'il ne prenne trop de place */
  overflow-y: auto !important;
  padding: 4px 0 !important;
}

/* Correction pour le portail du menu */
.react-select-menu-portal {
  z-index: 9999 !important;
}

.react-select__option {
  background-color: transparent !important;
  color: hsl(var(--nextui-foreground)) !important;
  cursor: pointer !important;
  padding: 8px 12px !important;
}

.react-select__option--is-focused {
  background-color: hsl(var(--nextui-primary-100)) !important;
}

.react-select__option--is-selected {
  background-color: hsl(var(--nextui-primary)) !important;
  color: white !important;
}

.react-select__placeholder {
  color: hsl(var(--nextui-default-500)) !important;
}

.react-select__input {
  color: hsl(var(--nextui-foreground)) !important;
}

.react-select__single-value {
  color: hsl(var(--nextui-foreground)) !important;
}

.react-select__multi-value {
  background-color: hsl(var(--nextui-primary-100)) !important;
  border-radius: 0.25rem !important;
}

.react-select__multi-value__label {
  color: hsl(var(--nextui-primary-700)) !important;
  font-size: 0.875rem !important;
  padding: 2px 6px !important;
}

.react-select__multi-value__remove {
  color: hsl(var(--nextui-primary-500)) !important;
  cursor: pointer !important;
}

.react-select__multi-value__remove:hover {
  background-color: hsl(var(--nextui-danger)) !important;
  color: white !important;
}

.react-select__indicator {
  color: hsl(var(--nextui-default-500)) !important;
}

.react-select__indicator:hover {
  color: hsl(var(--nextui-default-700)) !important;
}

.react-select__indicator-separator {
  background-color: hsl(var(--nextui-default-200)) !important;
}

.react-select__clear-indicator {
  color: hsl(var(--nextui-default-500)) !important;
}

.react-select__clear-indicator:hover {
  color: hsl(var(--nextui-danger)) !important;
}

.react-select__value-container {
  padding: 2px 8px !important;
}

.react-select__dropdown-indicator {
  padding: 0 8px !important;
}
