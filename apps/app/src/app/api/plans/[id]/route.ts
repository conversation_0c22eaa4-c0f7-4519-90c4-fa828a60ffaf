import { NextRequest, NextResponse } from "next/server"

import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const planId = parseInt(params.id)

    if (isNaN(planId)) {
      return NextResponse.json({ message: "ID de plan invalide" }, { status: 400 })
    }

    const plan = await prisma.plan.findUnique({
      where: { id: planId },
    })

    if (!plan) {
      return NextResponse.json({ message: "Plan non trouvé" }, { status: 404 })
    }

    return NextResponse.json(plan)
  } catch (error) {
    console.error("Error fetching plan:", error)
    return NextResponse.json({ message: "Une erreur est survenue" }, { status: 500 })
  }
}
