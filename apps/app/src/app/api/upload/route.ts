import { NextRequest, NextResponse } from "next/server"

import { env } from "@/lib/env"
import { prisma } from "@/lib/prisma"
import { logger } from "@coheadcoaching/lib"

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData()
    const file = formData.get("file") as File
    const url = formData.get("url") as string

    const fieldsJson = formData.get("fields") as string
    const fields = JSON.parse(fieldsJson) as Record<string, string>

    const uploadFormData = new FormData()
    // Ajouter tous les champs nécessaires pour S3
    Object.entries(fields).forEach(([key, value]) => {
      uploadFormData.append(key, value)
    })
    uploadFormData.append("file", file)

    // Upload vers S3
    const uploadResponse = await fetch(url, {
      method: "POST",
      body: uploadFormData,
    })

    if (!uploadResponse.ok) {
      const responseText = await uploadResponse.text()
      logger.error("S3 upload error:", responseText)
      return NextResponse.json(
        { error: "Upload failed", status: uploadResponse.status, details: responseText },
        { status: uploadResponse.status }
      )
    }

    // Créer l'entrée File dans la base de données
    const fileRecord = await prisma.file.create({
      data: {
        key: fields.key,
        filetype: file.type,
        bucket: fields.bucket || env.NEXT_PUBLIC_S3_BUCKET_NAME || "",
        endpoint: new URL(url).hostname,
        fileUploadingId: fields.fileUploadingId,
      },
    })

    // Construire l'URL publique
    const endpoint = new URL(url).hostname
    const publicUrl = `https://${endpoint}/${fields.key}`

    return NextResponse.json({
      success: true,
      key: fields.key,
      publicUrl,
      fileId: fileRecord.id, // Retourner l'ID du fichier créé
      id: fileRecord.id, // Pour compatibilité
    })
  } catch (error) {
    console.error("Upload error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
