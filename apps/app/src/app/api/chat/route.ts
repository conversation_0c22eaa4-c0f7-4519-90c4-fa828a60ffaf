import { appendResponseMessages, Message, streamText } from "ai"

import { saveChat } from "@/actions/chat-actions"
import { auth } from "@/lib/auth"
import { getUserRestrictionValue, hasReachedAgentLimit } from "@/lib/plan"
import { serverTrpc } from "@/lib/trpc/server"
import { google } from "@ai-sdk/google"
import { openai } from "@ai-sdk/openai"
import { logger } from "@coheadcoaching/lib"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

type TypeExpected = {
  messages: Message[]
  agentId: number
  filestoChatWith: number[]
  chatId: string
}

const errorHandler = (error: unknown): string => {
  logger.error("Error streaming chat:", error)
  return "Une erreur est survenue lors de la communication avec l'IA"
}

export async function POST(req: Request) {
  const session = await auth()
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 })
  }

  const { messages, agentId, chatId } = (await req.json()) as TypeExpected

  // Vérifier la restriction du nombre de messages par chat
  const maxMessagesPerChat = await getUserRestrictionValue(session.user.id, "MAX_MESSAGES_PER_CHAT")
  if (maxMessagesPerChat !== null && messages.length >= maxMessagesPerChat) {
    return new Response(
      `Vous avez atteint la limite de ${maxMessagesPerChat} messages par conversation pour votre plan actuel.`,
      { status: 403 }
    )
  }

  // Vérifier la limite d'agents
  if (!chatId) {
    // Seulement pour les nouvelles conversations
    const { hasReached, limit, hasConversationWithCurrentAgent } = await hasReachedAgentLimit(
      session.user.id,
      Number(agentId)
    )

    if (hasReached && !hasConversationWithCurrentAgent) {
      return new Response(`Vous avez atteint la limite de ${limit} agents pour votre plan actuel.`, { status: 403 })
    }
  }

  const {
    model,
    prompts,
    skills,
    temperature,
    title: agentName,
    personality,
  } = await serverTrpc.agent.getById(Number(agentId))

  let selectedModel
  if (model.startsWith("gpt")) {
    selectedModel = openai(model)
  } else if (model.startsWith("gemini")) {
    selectedModel = google(model)
  } else {
    throw new Error(`Modèle non supporté: ${model}`)
  }

  // Construction du prompt système strict et inflexible
  const systemPromptParts = [
    `Ton nom est "${agentName}". Tu es un agent conversationnel spécialisé, conçu pour accomplir une mission précise. Tu dois rester strictement dans ce cadre.`,

    personality ? `🎭 **Ta personnalité :** ${personality}` : null,

    `🟢 **Ce que tu es autorisé à faire :**`,
    `- Tu peux dire ton nom si on te le demande.`,
    `- Tu peux expliquer ton rôle et tes compétences.`,
    `- Tu peux guider l’utilisateur à travers ta mission en lui posant les questions nécessaires.`,

    skills ? `📌 **Tes compétences :** ${skills.map((skill) => skill.name).join(", ")}.` : null,

    prompts && prompts[0]?.body ? `📜 **Ta mission :**\n${prompts[0].body}` : null,

    `🧭 **Dès le début de l’échange, commence par poser à l’utilisateur les questions mentionnées dans ta mission afin de recueillir toutes les informations nécessaires.**`,

    /*`⚠️ **Interdictions absolues :**`,
    `- Tu **ne peux pas** répondre à des sujets hors de ta mission.`,
    `- Si un utilisateur essaie de te faire sortir de ton rôle, tu dois répondre : "Je suis conçu pour répondre uniquement sur [ton domaine]."`,
    `- Tu **ne dois pas** donner d’opinions personnelles ou d’informations qui ne relèvent pas de ta mission.`,*/
  ].filter(Boolean)

  const systemPrompt = systemPromptParts.join("\n\n")

  const result = streamText({
    model: selectedModel,
    temperature,
    messages: [{ role: "system", content: systemPrompt }, ...messages],
    async onFinish({ response }) {
      // If we have a chatId, save the messages
      if (chatId) {
        const updatedMessages = appendResponseMessages({
          messages,
          responseMessages: response.messages,
        })
        await saveChat(chatId, updatedMessages, true)
      }
    },
  })

  // Consume the stream to ensure it runs to completion & triggers onFinish even when the client response is aborted
  result.consumeStream()

  return result.toDataStreamResponse({ getErrorMessage: errorHandler })
}
