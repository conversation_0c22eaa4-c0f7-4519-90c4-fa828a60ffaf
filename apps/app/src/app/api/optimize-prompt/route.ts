import { NextResponse } from "next/server"
import { generateText } from "ai"

import { openai } from "@ai-sdk/openai"

export const maxDuration = 30

export async function POST(req: Request) {
  const body = (await req.json()) as { prompt: string }
  const { prompt } = body

  const { text } = await generateText({
    model: openai("gpt-4o"),
    prompt: `Tu es un assistant expert en reformulation et en optimisation de requêtes pour intelligence artificielle. Ton rôle est d’améliorer la clarté, la précision et la richesse des prompts qu’on te fournit afin d’obtenir des résultats plus pertinents et détaillés.\n
              Prends en compte les éléments suivants :\n\t
                - Clarifie toute ambiguïté ou imprécision.\n\t
                - Ajoute des détails pertinents pour enrichir la demande.\n\t
                - Reformule le prompt en conservant son intention initiale tout en le rendant plus efficace.\n
              Propose une version optimisée du prompt initial, sans ajouter d’éléments inutiles. Attention!!! Réponds seulement avec le prompt optimisé, rien d'autre. Voici le prompt à améliorer : \n---------------\n ${prompt} \n`,
  })

  return NextResponse.json({ optimizedPrompt: text })
}
