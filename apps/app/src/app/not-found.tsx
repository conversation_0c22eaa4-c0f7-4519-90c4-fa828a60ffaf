import React from "react"
import { cookies } from "next/headers"
import { Home, Search } from "lucide-react"

import { Icons } from "@/components/icons"
import { fontSans } from "@/lib/fonts"
import { i18n, Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { cn } from "@/lib/utils"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardFooter, CardHeader } from "@nextui-org/card"
import { Link } from "@nextui-org/link"

import UIProvider from "./[lang]/ui-provider"

import "./globals.css"

export default async function Page404MatchAll() {
  const cookiesStore = cookies()
  const savedLocale = cookiesStore.get("saved-locale")
  const params = savedLocale?.value ? { lang: savedLocale.value } : undefined
  const dictionary = await getDictionary(params ? (params.lang as Locale) : i18n.defaultLocale, {
    notFound: true,
    goHome: true,
    app: { name: true },
  })

  return (
    <html lang={params?.lang ?? i18n.defaultLocale}>
      <body
        className={cn("h-dvh min-h-dvh bg-background font-sans antialiased", fontSans.variable, fontSans.className)}
      >
        <UIProvider>
          <main className="container m-auto flex min-h-screen flex-1 flex-col items-center justify-center p-4">
            <Card className="w-full max-w-md border border-foreground/10 bg-background/60 backdrop-blur-md">
              <CardHeader className="flex flex-col items-center gap-2 pb-0">
                <div className="flex w-full justify-center">
                  <Icons.logo className="size-16 text-primary" />
                </div>
                <h1 className="text-center text-3xl font-bold">{dictionary.notFound}</h1>
              </CardHeader>
              <CardBody className="flex flex-col items-center py-8">
                <div className="relative mb-6 aspect-square w-full max-w-[280px]">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Search className="size-24 text-primary/20" strokeWidth={1} />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-8xl font-bold text-primary/80">404</span>
                  </div>
                </div>
                <p className="mb-6 text-center text-foreground-500">
                  La page que vous recherchez semble introuvable. Elle a peut-être été déplacée ou supprimée.
                </p>
              </CardBody>
              <CardFooter className="flex justify-center pb-6">
                <Button
                  as={Link}
                  href="/"
                  color="primary"
                  variant="flat"
                  startContent={<Home size={18} />}
                  className="px-6 font-medium"
                >
                  {dictionary.goHome}
                </Button>
              </CardFooter>
            </Card>
          </main>
        </UIProvider>
      </body>
    </html>
  )
}
