import NavSettings from "@/components/nav-settings"
import CategorySelectionCheck from "@/components/profile/CategorySelectionCheck"
import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"

import Agents from "./agents"

export default async function Home({
  params: { lang },
}: {
  params: {
    lang: Locale
  }
}) {
  const session = await auth()

  return (
    <main className="container m-auto flex min-h-screen flex-1 flex-col items-center gap-3">
      <NavSettings lang={lang} />

      {/* Ajouter le composant de vérification des catégories si l'utilisateur est connecté */}
      {session?.user && <CategorySelectionCheck forceSelection={false} />}

      <div className="w-full p-6">
        <div className="w-full max-w-[1200px] place-self-center">
          <h2 className="mb-2 text-3xl font-bold">Les agents</h2>
          <p className="text-gray-400">Explorez l&apos;ensemble des agents GPT disponibles sur notre plateforme!</p>
        </div>
      </div>
      <div className="w-full p-6">
        <div className="flex w-full max-w-[1200px] flex-row gap-4 place-self-center max-lg:flex-col lg:gap-20">
          <Agents />
        </div>
      </div>
    </main>
  )
}
