import React from "react"

import { FormsTable } from "@/components/forms/admin/forms-table"
import { serverTrpc } from "@/lib/trpc/server"

export default async function FormsPage() {
  const { data: forms, pagination } = await serverTrpc.form.getAll({
    page: 1,
    pageSize: 15,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Gestion des formulaires</h2>
      <p className="mb-6 text-gray-400">
        <PERSON><PERSON><PERSON> et gérez les formulaires dynamiques pour collecter des informations des utilisateurs.
      </p>
      <FormsTable initialForms={forms} initialPagination={pagination} />
    </>
  )
}
