import React from "react"
import { notFound } from "next/navigation"

import { FormAnalyticsDashboard } from "@/components/forms/admin/form-analytics-dashboard"
import { serverTrpc } from "@/lib/trpc/server"

interface FormAnalyticsPageProps {
  params: {
    id: string
  }
}

export default async function FormAnalyticsPage({ params }: FormAnalyticsPageProps) {
  try {
    // Verify the form exists
    const form = await serverTrpc.form.getById(params.id)
    
    if (!form) {
      notFound()
    }

    return <FormAnalyticsDashboard formId={params.id} />
  } catch (error) {
    notFound()
  }
}
