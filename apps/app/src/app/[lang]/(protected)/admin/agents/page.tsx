import React from "react"

import { AgentsTable } from "@/components/table-agents/agents-table"
import { serverTrpc } from "@/lib/trpc/server"

const AgentsPage = async () => {
  const { data: agents, pagination } = await serverTrpc.agent.getAllForAdmin({
    page: 1,
    pageSize: 15,
    withBadge: true,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Agents</h2>
      <p className="mb-2 text-gray-400">Gérez l&apos;ensemble des agents GPT disponibles sur votre plateforme!</p>
      <AgentsTable initialAgents={agents} initialPagination={pagination} />
    </>
  )
}

export default AgentsPage
