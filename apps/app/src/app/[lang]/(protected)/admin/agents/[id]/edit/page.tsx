import React from "react"
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"

import AdminChatbox from "@/components/adminChatbox"
import AgentTabs from "@/components/agent/agents-tabs"
import AgentConfigAdmin from "@/components/agentConfigAdmin"
import { serverTrpc } from "@/lib/trpc/server"
import { Button } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Spinner } from "@nextui-org/spinner"

interface AgentPageProps {
  params: {
    id: string
  }
}

const AgentPage: React.FC<AgentPageProps> = async ({ params }) => {
  const agentId = Number(params.id)
  if (isNaN(agentId)) {
    return <div>Invalid agent ID</div>
  }

  // Fetch agent data
  const agent = await serverTrpc.agent.getByIdForAdmin(Number(agentId))

  if (!agent) {
    return (
      <div className="container mx-auto flex h-[80vh] items-center justify-center p-6">
        <Card className="max-w-md">
          <CardBody className="flex flex-col items-center gap-4 p-8 text-center">
            <Spinner size="lg" />
            <h2 className="text-xl font-semibold">Agent non trouvé</h2>
            <p className="text-default-500">
              Quelque chose s&apos;est mal passé lors de la récupération de l&apos;agent
            </p>
            <Button color="primary" href="/admin" as="a">
              Retour au tableau de bord
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }
  return (
    <div className="container mx-auto px-4 py-6 lg:px-6">
      {/* Header avec navigation */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-3">
          <Button isIconOnly variant="light" as="a" href="/admin" aria-label="Retour">
            <ArrowLeft />
          </Button>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold sm:text-3xl">
                {agent.icon} {agent.title}
              </h1>
              {agent.badge && (
                <Chip size="sm" variant="flat" color="warning">
                  {agent.badge.title}
                </Chip>
              )}
            </div>
            <p className="text-sm text-default-500">ID: {agent.id}</p>
          </div>
        </div>
        <Button color="primary" variant="flat" as="a" href="/admin/agents">
          Tous les agents
        </Button>
      </div>

      {/* Contenu principal: Responsive avec onglets sur mobile, colonnes sur desktop */}
      <div className="block lg:hidden">
        <AgentTabs agent={agent} />
      </div>

      <div className="hidden grid-cols-1 gap-6 lg:grid lg:grid-cols-2">
        {/* Configuration de l'agent */}
        <Card className="h-fit">
          <CardBody>
            <AgentConfigAdmin agent={agent} />
          </CardBody>
        </Card>

        {/* Chatbox pour tester l'agent */}
        <Card className="h-fit">
          <CardBody className="p-0">
            <AdminChatbox agent={agent} />
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default AgentPage
