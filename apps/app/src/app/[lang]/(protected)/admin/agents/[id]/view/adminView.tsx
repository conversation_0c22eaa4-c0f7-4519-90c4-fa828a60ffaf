"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { toast } from "react-toastify"

import AgentDetailsView from "@/components/agent/AgentDetailsView"
import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON><PERSON>, ModalBody, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Spinner } from "@nextui-org/spinner"

interface AgentViewProps {
  agentId: number
}

const AgentView = ({ agentId }: AgentViewProps) => {
  const router = useRouter()

  const [showDeleteModal, setShowDeleteModal] = useState(false)

  // Récupérer les données de l'agent
  const {
    data: agent,
    isLoading,
    error,
  } = trpc.agent.getByIdForAdmin.useQuery(agentId, {
    enabled: !isNaN(agentId),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Mutation pour supprimer l'agent
  const deleteAgent = trpc.agent.delete.useMutation({
    onSuccess: () => {
      toast.success("Agent supprimé avec succès!")
      router.push("/admin/agents")
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression de l'agent: ${error.message}`)
    },
  })

  // Gérer la confirmation de suppression
  const handleDeleteClick = () => {
    setShowDeleteModal(true)
  }

  const handleConfirmDelete = () => {
    if (agent) {
      deleteAgent.mutate(String(agent.id))
    }
  }

  // Afficher un message d'erreur si nécessaire
  useEffect(() => {
    if (error) {
      toast.error(`Erreur lors du chargement de l'agent: ${error.message}`)
    }
  }, [error])

  if (isLoading) {
    return (
      <div className="flex h-[70vh] items-center justify-center">
        <Spinner size="lg" label="Chargement des informations de l'agent..." />
      </div>
    )
  }

  if (!agent) {
    return (
      <div className="container mx-auto max-w-5xl px-4 py-6 text-center">
        <h1 className="text-xl font-bold">Agent non trouvé</h1>
        <p className="mt-2 text-gray-600">L&apos;agent que vous recherchez n&apos;existe pas ou a été supprimé.</p>
        <Button className="mt-4" color="primary" onPress={() => router.push("/admin/agents")}>
          Retour à la liste des agents
        </Button>
      </div>
    )
  }

  return (
    <>
      <AgentDetailsView agent={agent} onDeleteClick={handleDeleteClick} isAdmin={true} />

      {/* Modal de confirmation de suppression */}
      <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <ModalContent>
          <ModalHeader>
            <h2 className="text-lg font-bold">Confirmation de suppression</h2>
          </ModalHeader>
          <ModalBody>
            Êtes-vous sûr de vouloir supprimer l&apos;agent {agent.title} ? Cette action est irréversible.
          </ModalBody>
          <ModalFooter className="flex w-full justify-between">
            <Button onPress={() => setShowDeleteModal(false)}>Annuler</Button>
            <Button isLoading={deleteAgent.isPending} onPress={handleConfirmDelete} variant="solid" color="danger">
              Supprimer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default AgentView
