import React from "react"
import { Metada<PERSON> } from "next"

import AgentCreator from "./agentCreator"

export const metadata: Metadata = {
  title: "Créer un agent | Dashboard admin",
  description: "Créer un nouvel agent avec une configuration personnalisée",
}

const CreateAgentPage: React.FC = () => {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Créer un nouvel agent</h1>
        <p className="text-gray-600">
          Créer un nouvel agent en fournissant des informations de base et une configuration personnalisée
        </p>
      </div>

      <AgentCreator />
    </div>
  )
}

export default CreateAgentPage
