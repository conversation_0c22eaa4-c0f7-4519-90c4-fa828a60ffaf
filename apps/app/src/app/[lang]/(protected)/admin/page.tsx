import { DollarSign, Star, Ticket, User<PERSON><PERSON>, Users } from "lucide-react"

import { prisma } from "@/lib/prisma"
import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card"

async function getStats() {
  const [totalAgents, totalUsers, totalTickets, usersWithFavorites, allAgents] = await Promise.all([
    prisma.agent.count(),
    prisma.user.count(),
    prisma.supportTicket.count(),
    prisma.user.findMany({
      select: {
        favoriteAgentIds: true,
      },
    }),
    prisma.agent.findMany({
      select: {
        id: true,
        title: true,
      },
    }),
  ])

  // Get message counts per agent
  const agentMessageCounts = await prisma.$queryRaw<{ agentId: number; message_count: bigint }[]>`
    SELECT c."agentId", COUNT(m.id) as message_count
    FROM "Chat" c
    JOIN "Message" m ON m."chatId" = c.id
    WHERE c."agentId" IS NOT NULL
    GROUP BY c."agentId"
  `

  // Calculate agent favorites
  const agentFavorites = usersWithFavorites.reduce((acc: { [key: number]: number }, user) => {
    user.favoriteAgentIds.forEach((agentId) => {
      acc[agentId] = (acc[agentId] || 0) + 1
    })
    return acc
  }, {})

  // Convert message counts to lookup object
  const messageCounts = agentMessageCounts.reduce(
    (acc, { agentId, message_count }) => {
      acc[agentId] = Number(message_count)
      return acc
    },
    {} as Record<number, number>
  )

  // Calculate popularity score for each agent (70% favorites, 30% messages)
  const agentsWithScores = allAgents.map((agent) => {
    const favorites = agentFavorites[agent.id] || 0
    const messages = messageCounts[agent.id] || 0

    // Normalize scores (using max values or default to 1 to avoid division by zero)
    const maxFavorites = Math.max(1, ...Object.values(agentFavorites))
    const maxMessages = Math.max(1, ...Object.values(messageCounts))

    const normalizedFavorites = (favorites / maxFavorites) * 0.7
    const normalizedMessages = (messages / maxMessages) * 0.3

    const popularityScore = normalizedFavorites + normalizedMessages

    return {
      ...agent,
      favorites,
      messages,
      popularityScore,
    }
  })

  // Sort by popularity score
  agentsWithScores.sort((a, b) => b.popularityScore - a.popularityScore)

  // Get top 5 most popular agents
  const popularAgents = agentsWithScores.slice(0, 5).filter((agent) => agent.popularityScore > 0)

  // TODO: Implement revenue calculation based on your business logic
  const monthlyRevenue = 0
  const totalRevenue = 0

  return {
    totalAgents,
    totalUsers,
    monthlyRevenue,
    totalRevenue,
    popularAgents,
    totalTickets,
  }
}

export default async function Home() {
  const stats = await getStats()

  return (
    <main className="container m-auto flex min-h-screen flex-1 flex-col gap-6 p-6">
      <h1 className="text-3xl font-bold">Tableau de bord</h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="text-sm font-medium">Agents</h3>
            <UserCog className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold">{stats.totalAgents}</div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="text-sm font-medium">Utilisateurs</h3>
            <Users className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="text-sm font-medium">Chiffre d&apos;affaires</h3>
            <DollarSign className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold">{stats.monthlyRevenue}€</div>
            <p className="text-xs text-muted-foreground">Total: {stats.totalRevenue}€</p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="text-sm font-medium">Tickets Support</h3>
            <Ticket className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold">{stats.totalTickets}</div>
          </CardBody>
        </Card>
      </div>

      <Card className="col-span-4">
        <CardHeader className="flex-col items-start">
          <h3 className="text-xl font-semibold">Agents les plus populaires</h3>
          <p className="text-sm text-muted-foreground">
            Le score de popularité est calculé en combinant deux facteurs : les favoris des utilisateurs (70% du score)
            et le nombre de messages échangés (30% du score). Les valeurs sont normalisées pour assurer une comparaison
            équitable entre agents.
          </p>
        </CardHeader>
        <CardBody>
          {stats.popularAgents.length === 0 ? (
            <p className="text-sm text-muted-foreground">Aucun agent populaire pour le moment</p>
          ) : (
            <div className="space-y-4">
              {stats.popularAgents.map((agent) => (
                <div key={agent.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Star className="size-4 text-yellow-400" />
                      <span>{agent.favorites}</span>
                      <span className="text-sm text-muted-foreground">({agent.messages} messages)</span>
                    </div>
                    <span>{agent.title}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Score: {(agent.popularityScore * 100).toFixed(1)}%
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </main>
  )
}
