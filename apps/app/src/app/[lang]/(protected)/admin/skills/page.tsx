import { SkillsTable } from "@/components/skills/skillsTable"
import { serverTrpc } from "@/lib/trpc/server"

export default async function Users() {
  const { data: skills, pagination } = await serverTrpc.skill.getAllForAdmin({
    page: 1,
    pageSize: 15,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Skills</h2>
      <p className="mb-2 text-gray-400">Gérez l&apos;ensemble des skills de votre plateforme depuis un seul endroit!</p>
      <SkillsTable initialSkills={skills} initialPagination={pagination} />
    </>
  )
}
