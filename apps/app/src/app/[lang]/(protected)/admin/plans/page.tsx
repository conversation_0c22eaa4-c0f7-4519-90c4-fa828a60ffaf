import React from "react"

import { PlansTable } from "@/components/plans/plansTable"
import { serverTrpc } from "@/lib/trpc/server"

export default async function PlansPage() {
  const { data: plans, pagination } = await serverTrpc.plan.getAllForAdmin({
    page: 1,
    pageSize: 15,
  })
  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Plans</h2>
      <p className="mb-2 text-gray-400">Gérez l&apos;ensemble des plans d&apos;abonnement de votre plateforme!</p>
      <PlansTable initialPlans={plans} initialPagination={pagination} />
    </>
  )
}
