import { BadgesTable } from "@/components/badges/badgesTable"
import { serverTrpc } from "@/lib/trpc/server"

export default async function Users() {
  const { data: badges, pagination } = await serverTrpc.badge.getAllForAdmin({
    page: 1,
    pageSize: 15,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Badges</h2>
      <p className="mb-2 text-gray-400">Gérez l&apos;ensemble des badges de votre plateforme depuis un seul endroit!</p>
      <BadgesTable initialBadges={badges} initialPagination={pagination} />
    </>
  )
}
