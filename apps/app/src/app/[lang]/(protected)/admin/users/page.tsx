import { UsersTable } from "@/components/table-users/users-table"
import { serverTrpc } from "@/lib/trpc/server"
import { logger } from "@coheadcoaching/lib"

export default async function Users() {
  const { data: users, pagination } = await serverTrpc.user.getAll({
    page: 1,
    pageSize: 15,
  })

  logger.log(...users)

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Utilisateurs</h2>
      <p className="mb-2 text-gray-400">
        Gérez l&apos;ensemble des utilisateurs de votre plateforme depuis un seul endroit!
      </p>
      <UsersTable initialUsers={users} initialPagination={pagination} />
    </>
  )
}
