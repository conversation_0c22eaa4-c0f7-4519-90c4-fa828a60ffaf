import React from "react"

import { SubscriptionsTable } from "@/components/subscriptions/admin/subscriptionsTable"
import { serverTrpc } from "@/lib/trpc/server"

export default async function SubscriptionsPage() {
  const { data: subscriptions, pagination } = await serverTrpc.subscription.getAllForAdmin({
    page: 1,
    pageSize: 15,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Abonnements</h2>
      <p className="mb-2 text-gray-400">
        Gérez l&apos;ensemble des abonnements de votre plateforme depuis un seul endroit!
      </p>
      <SubscriptionsTable initialSubscriptions={subscriptions} initialPagination={pagination} />
    </>
  )
}
