"use server"

import React from "react"
import Link from "next/link"
import { CheckCircle2, <PERSON>, Settings } from "lucide-react"

import CategorySelectionCheck from "@/components/profile/CategorySelectionCheck"
import { auth } from "@/lib/auth"
import { getUserRestrictionValue } from "@/lib/plan"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"

export default async function SubscriptionSuccessPage() {
  const session = await auth()
  const userId = session?.user?.id

  // Vérifier si l'utilisateur a un plan avec des catégories restreintes
  let hasCategoryRestrictions = false
  if (userId) {
    const maxCategories = await getUserRestrictionValue(userId, "MAX_CATEGORIES")
    hasCategoryRestrictions = maxCategories !== null
  }

  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20 sm:px-6 lg:px-8">
      {/* Centrage vertical approximatif */}
      <Card className="w-full overflow-hidden border-t-4 border-success shadow-xl">
        <CardHeader className="justify-center bg-success-50 p-6 dark:bg-success-900/20">
          <CheckCircle2 className="size-16 text-success" strokeWidth={1.5} />
        </CardHeader>
        <CardBody className="flex flex-col items-center p-8 text-center sm:p-12">
          <h1 className="mb-3 text-3xl font-semibold text-gray-800 dark:text-gray-100">Félicitations !</h1>
          <p className="mb-6 text-lg font-medium text-success-700 dark:text-success-400">
            Votre abonnement est activé.
          </p>

          <p className="mb-10 max-w-md text-default-600 dark:text-default-300">
            Vous avez maintenant accès à toutes les fonctionnalités premium incluses dans votre plan. Prêt à démarrer ?
          </p>

          {/* Afficher le composant de sélection de catégories si l'utilisateur a des restrictions */}
          {hasCategoryRestrictions && (
            <>
              <div className="mb-6 w-full max-w-md">
                <h2 className="mb-2 text-lg font-medium">Personnalisez votre expérience</h2>
                <p className="mb-4 text-sm text-default-600">
                  Votre plan vous permet de sélectionner des catégories spécifiques. Choisissez les catégories qui vous
                  intéressent pour découvrir des agents adaptés à vos besoins.
                </p>
                <CategorySelectionCheck forceSelection={true} />
              </div>
              <Divider className="mx-auto my-4 w-1/2" />
            </>
          )}

          <div className="mt-6 flex w-full flex-col gap-4 sm:w-auto sm:flex-row">
            <Link href={"/"} passHref>
              <Button color="primary" startContent={<Rocket size={18} />} className="w-full sm:w-auto">
                Accéder à l&apos;accueil
              </Button>
            </Link>
            <Link href={"/profile"} passHref>
              <Button variant="ghost" startContent={<Settings size={18} />} className="w-full sm:w-auto">
                Gérer mon abonnement
              </Button>
            </Link>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
