import React from "react"
import { redirect } from "next/navigation"

import { serverTrpc } from "@/lib/trpc/server"

import SetupForm from "./components/setup-form"

export default async function SubscriptionSetupPage({
  searchParams,
}: {
  searchParams: { planId?: string; period?: string }
}) {
  const planId = searchParams.planId
  const period = searchParams.period as "MONTHLY" | "ANNUAL"

  if (!planId || !period) {
    redirect("/pricing")
  }

  // Vérifier si l'utilisateur a déjà un compte MangoPay
  const setupStatus = await serverTrpc.subscription.checkSetup()

  return (
    <div className="container mx-auto max-w-2xl py-8">
      <h1 className="mb-6 text-2xl font-bold">Configuration de votre abonnement</h1>

      <SetupForm
        planId={planId}
        period={period}
        hasMangopayAccount={setupStatus.hasMangopayAccount}
        hasCard={setupStatus.hasCard}
      />
    </div>
  )
}
