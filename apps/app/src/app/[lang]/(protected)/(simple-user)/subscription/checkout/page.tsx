import React, { Suspense } from "react"
import { redirect } from "next/navigation"
import { CheckCircle2, Lock } from "lucide-react"

import { serverTrpc } from "@/lib/trpc/server"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Spinner } from "@nextui-org/spinner"

import CheckoutForm from "./components/checkout-form"

// Composant de chargement
function CheckoutLoading() {
  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20">
      <Card className="w-full p-8 text-center">
        <CardBody className="flex flex-col items-center gap-6">
          <Spinner size="lg" color="primary" />
          <h2 className="text-xl font-semibold">Chargement de votre paiement</h2>
          <p className="text-default-500">Veuillez patienter pendant que nous préparons votre commande...</p>
        </CardBody>
      </Card>
    </div>
  )
}

// Composant principal avec la logique de checkout
async function CheckoutContent({ searchParams }: { searchParams: { planId?: string; period?: string } }) {
  const planId = searchParams.planId
  const period = searchParams.period as "MONTHLY" | "ANNUAL"

  if (!planId || !period) {
    redirect("/pricing")
  }

  const planData = await serverTrpc.plan.getById(Number(planId))
  if (!planData) {
    redirect("/pricing")
  }

  const setupStatus = await serverTrpc.subscription.checkSetup()

  if (!setupStatus.hasMangopayAccount || !setupStatus.hasCard) {
    redirect(`/subscription/setup?planId=${planId}&period=${period}`)
  }

  const price = period === "MONTHLY" ? planData.monthlyPrice : planData.annualPrice
  const periodText = period === "MONTHLY" ? "mois" : "an"
  const defaultCard =
    setupStatus.user?.cards?.findLast((card) => card.isDefault) ||
    setupStatus.user?.cards?.findLast((card) => card.mangopayCardId)

  if (!defaultCard?.mangopayCardId) {
    console.error("Aucune carte par défaut ou valide trouvée pour l'utilisateur.")
    redirect(`/subscription/setup?planId=${planId}&period=${period}&error=no_card`)
  }

  return (
    <div className="container mx-auto max-w-3xl px-4 py-12 sm:px-6 lg:px-8">
      <h1 className="mb-8 text-center text-3xl font-bold text-gray-800 dark:text-gray-100">
        Finalisez votre abonnement
      </h1>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {/* Colonne 1: Détails du plan */}
        <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
          <CardHeader className="flex-col items-start px-4 pb-0 pt-4">
            <p className="text-tiny font-bold uppercase text-default-500">Votre Sélection</p>
            <h2 className="mt-1 text-large font-bold">{planData.name}</h2>
          </CardHeader>
          <CardBody className="overflow-visible py-2">
            <p className="mb-4 text-sm text-default-500">{planData.description}</p>
            <div className="mb-4">
              <span className="text-3xl font-extrabold text-primary">
                {Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)}
              </span>
              <span className="text-default-500">/{periodText}</span>
            </div>

            {planData.features && planData.features.length > 0 && (
              <>
                <Divider className="my-4" />
                <p className="mb-3 text-sm font-semibold text-default-600">Fonctionnalités clés :</p>
                <ul className="space-y-2">
                  {planData.features.map(
                    (feature, index) =>
                      feature.included && (
                        <li key={index} className="flex items-center text-sm">
                          <CheckCircle2 className="mr-2 size-5 shrink-0 text-success" />
                          <span>{feature.text}</span>
                        </li>
                      )
                  )}
                </ul>
              </>
            )}
          </CardBody>
        </Card>

        {/* Colonne 2: Paiement */}
        <div className="space-y-6">
          <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Confirmer le paiement</h2>
            </CardHeader>
            <CardBody>
              <p className="mb-4 text-sm text-default-600">
                Votre paiement sera traité de manière sécurisée via notre partenaire MangoPay.
              </p>
              <div className="mb-6 rounded-lg bg-default-100 p-4 dark:bg-default-200/50">
                <p className="mb-2 text-sm font-medium text-default-700">Payer avec :</p>
                <div className="flex items-center justify-between">
                  <span className="font-mono text-sm tracking-wider text-default-800">
                    Carte se terminant par {defaultCard.last4}
                  </span>
                  <span className="text-xs text-default-500">
                    Exp: {defaultCard.expirationDate?.slice(0, 2) + "/" + defaultCard.expirationDate?.slice(2, 4)}
                  </span>
                </div>
              </div>

              {/* Intégration du formulaire client */}
              <CheckoutForm
                planId={Number(planId)}
                period={period}
                cardId={defaultCard.id}
                mangopayCardId={defaultCard.mangopayCardId}
                price={price}
                periodText={periodText}
              />
            </CardBody>
          </Card>
          <div className="flex items-center justify-center gap-2 text-center text-xs text-default-500">
            <Lock className="size-4 text-success" />
            <span>Transactions sécurisées</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage({ searchParams }: { searchParams: { planId?: string; period?: string } }) {
  return (
    <Suspense fallback={<CheckoutLoading />}>
      <CheckoutContent searchParams={searchParams} />
    </Suspense>
  )
}
