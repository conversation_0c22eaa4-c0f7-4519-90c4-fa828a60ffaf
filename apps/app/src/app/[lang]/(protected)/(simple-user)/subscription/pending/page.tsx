import React from "react"
import Link from "next/link"
import { Clock, HelpCircle, Home } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"

export default function SubscriptionPendingPage() {
  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20 sm:px-6 lg:px-8">
      <Card className="w-full overflow-hidden border-t-4 border-warning shadow-xl">
        <CardHeader className="justify-center bg-warning-50 p-6 dark:bg-warning-900/20">
          <Clock className="size-16 text-warning" strokeWidth={1.5} />
        </CardHeader>
        <CardBody className="flex flex-col items-center p-8 text-center sm:p-12">
          <h1 className="mb-3 text-3xl font-semibold text-gray-800 dark:text-gray-100">
            Paiement en cours de traitement
          </h1>
          <p className="mb-6 text-lg font-medium text-warning-700 dark:text-warning-400">
            Votre paiement est en attente de confirmation.
          </p>

          <p className="mb-4 text-default-500">
            Cela peut prendre quelques instants. Vous recevrez une confirmation par email dès que votre abonnement sera
            activé.
          </p>
          <p className="mb-6 text-default-500">
            Vous pouvez revenir à l&apos;accueil ou contacter notre support si vous avez des questions.
          </p>

          <Divider className="mx-auto my-6 w-1/2" />

          <div className="mt-6 flex w-full flex-col gap-4 sm:w-auto sm:flex-row">
            <Link href={"/dashboard"} passHref>
              <Button color="primary" variant="solid" startContent={<Home size={18} />} className="w-full sm:w-auto">
                Retour à l&apos;accueil
              </Button>
            </Link>
            <Link href={"/support"} passHref>
              <Button variant="ghost" startContent={<HelpCircle size={18} />} className="w-full sm:w-auto">
                Contacter le support
              </Button>
            </Link>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
