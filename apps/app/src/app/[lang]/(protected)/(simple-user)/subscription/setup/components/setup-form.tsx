"use client"

import React, { use<PERSON>em<PERSON>, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { CountryDropdown, RegionDropdown } from "react-country-region-selector"
import { usePaymentInputs } from "react-payment-inputs"
import images from "react-payment-inputs/images"
import { toast } from "react-toastify"
import { z } from "zod"

import { createMangopayUser, registerCard } from "@/actions/mangopay-actions"
import { updateUserProfile } from "@/actions/user-actions"
import { WHITE_LISTED_COUNTRIES } from "@/lib/mangopay"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Input } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Tab, Tabs } from "@nextui-org/tabs"
import { cn } from "@nextui-org/theme"

type SubscriptionPeriod = "MONTHLY" | "ANNUAL"

export type TargetCardType = "CB_VISA_MASTERCARD" | "AMEX" | "MAESTRO" | "BCMC"

interface AddressData {
  addressLine1: string
  addressLine2?: string | null
  city: string
  postalCode: string
  country: string
  region: string
}
interface UserFormData extends AddressData {
  firstName: string
  lastName: string
  email: string
  country: string
  region: string
}

interface CardFormData {
  cardNumber: string
  expiryDate: string
  cvc: string
}

interface SetupFormProps {
  planId: string
  period: SubscriptionPeriod
  hasMangopayAccount: boolean
  hasCard: boolean
}

const errorMessagesFr = {
  emptyCardNumber: "Le numéro de carte est requis.",
  invalidCardNumber: "Le numéro de carte est invalide.",
  emptyExpiryDate: "La date d'expiration est requise.",
  monthOutOfRange: "Le mois d'expiration doit être compris entre 01 et 12.",
  yearOutOfRange: "L'année d'expiration ne peut pas être dans le passé.",
  dateOutOfRange: "La date d'expiration ne peut pas être dans le passé.",
  invalidExpiryDate: "La date d'expiration est invalide.",
  emptyCVC: "Le CVC est requis.",
  invalidCVC: "Le CVC est invalide.",
  unsupportedCardType:
    "Ce type de carte n'est pas accepté. Veuillez utiliser CB, Visa, Mastercard, American Express, Maestro ou Bancontact.",
}

const userFormSchema = z.object({
  firstName: z.string().min(1, { message: "Le prénom est requis." }),
  lastName: z.string().min(1, { message: "Le nom est requis." }),
  email: z.string().min(1, { message: "L'email est requis." }).email({ message: "L'adresse email est invalide." }),
  addressLine1: z.string().min(1, { message: "L'adresse est requise." }),
  addressLine2: z.string().optional().nullable(),
  city: z.string().min(1, { message: "La ville est requise." }),
  postalCode: z
    .string()
    .min(1, { message: "Le code postal est requis." })
    .max(12, { message: "Le code postal ne doit pas dépasser 12 caractères." }),
  country: z.string().min(1, { message: "Le pays est requis." }),
  region: z.string().min(1, { message: "La région est requise" }),
})

type UserFormErrors = Partial<Record<keyof UserFormData, string>>

const SUPPORTED_LIBRARY_CARD_TYPES = ["cb", "visa", "mastercard", "amex", "maestro", "bancontact"] as const

type LibraryCardType = (typeof SUPPORTED_LIBRARY_CARD_TYPES)[number]

function isSupportedLibraryCardType(type: string): type is LibraryCardType {
  return (SUPPORTED_LIBRARY_CARD_TYPES as readonly string[]).includes(type)
}

function mapLibraryTypeToTargetType(libraryCardType: LibraryCardType): TargetCardType {
  switch (libraryCardType) {
    case "cb":
    case "visa":
    case "mastercard":
      return "CB_VISA_MASTERCARD"
    case "amex":
      return "AMEX"
    case "maestro":
      return "MAESTRO"
    case "bancontact":
      return "BCMC"
  }
}

// @ts-expect-error Binding Els type
const CustomRender = ({ options, customProps, ...selectProps }) => (
  <Select {...selectProps} {...customProps}>
    {options.map(({ label, value }: { label: string; value: string }) => (
      <SelectItem value={value} key={value}>
        {label}
      </SelectItem>
    ))}
  </Select>
)

export default function SetupForm({ planId, period, hasMangopayAccount, hasCard }: SetupFormProps) {
  const router = useRouter()
  const initialStep = hasMangopayAccount ? "card" : "user"
  const [step, setStep] = useState<"user" | "card">(initialStep)
  const [isLoading, setIsLoading] = useState(false)
  const [serverError, setServerError] = useState<string | null>(null)
  const [cardValidationError, setCardValidationError] = useState<string | null>(null)
  const [userFormErrors, setUserFormErrors] = useState<UserFormErrors>({})

  const session = useSession()

  const [userData, setUserData] = useState<UserFormData>({
    firstName: "",
    lastName: "",
    email: session.data?.user.email || "",
    addressLine1: "",
    addressLine2: "",
    city: "",
    postalCode: "",
    country: "",
    region: "",
  })
  const [cardData, setCardData] = useState<CardFormData>({ cardNumber: "", expiryDate: "", cvc: "" })

  const { meta, getCardNumberProps, getExpiryDateProps, getCVCProps, getCardImageProps } = usePaymentInputs({
    errorMessages: errorMessagesFr,
  })

  const CardIcon = useMemo(() => {
    //@ts-expect-error type of import not found
    const props = getCardImageProps({ images })
    props.width = "35"
    props.height = "auto"
    return <svg {...props} className="pt-4" />
  }, [getCardImageProps])

  if (hasMangopayAccount && hasCard) {
    router.push(`/subscription/checkout?planId=${planId}&period=${period}`)
    return null
  }

  const clearValidationErrors = () => {
    if (serverError) setServerError(null)
    if (cardValidationError) setCardValidationError(null)
    if (Object.keys(userFormErrors).length > 0) setUserFormErrors({})
  }

  const handleUserInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setUserData((prev) => ({ ...prev, [name]: value }))
    if (userFormErrors[name as keyof UserFormData]) {
      setUserFormErrors((prev) => ({ ...prev, [name]: undefined }))
    }
    if (serverError) setServerError(null)
  }

  const handleUserSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setUserFormErrors({})
    setServerError(null)

    const validationResult = userFormSchema.safeParse(userData)

    if (!validationResult.success) {
      const errors: UserFormErrors = {}
      validationResult.error.issues.forEach((issue) => {
        const path = issue.path[0] as keyof UserFormData
        if (path) {
          errors[path] = issue.message
        }
      })
      setUserFormErrors(errors)
      toast.warning("Veuillez corriger les erreurs dans le formulaire.")
      return
    }

    setIsLoading(true)
    try {
      const address: AddressData = {
        addressLine1: validationResult.data.addressLine1,
        addressLine2: validationResult.data.addressLine2 || null,
        city: validationResult.data.city,
        postalCode: validationResult.data.postalCode,
        country: validationResult.data.country,
        region: validationResult.data.region,
      }
      await createMangopayUser({
        firstName: validationResult.data.firstName,
        lastName: validationResult.data.lastName,
        email: validationResult.data.email,
        address: address,
      })

      try {
        await updateUserProfile(session?.data?.user.id as string, {
          name: `${validationResult.data.firstName} ${validationResult.data.lastName}`,
        })
      } catch (error) {
        logger.warn("Failed to update user profile", { error })
      }

      setStep("card")
      toast.success("Vos informations ont été enregistrées.")
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Erreur lors de l'enregistrement des informations utilisateur."
      setServerError(message)
      toast.error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, cardNumber: e.target.value }))
    if (cardValidationError) setCardValidationError(null)
    if (serverError) setServerError(null)
  }
  const handleExpiryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, expiryDate: e.target.value }))
    if (serverError) setServerError(null)
  }
  const handleCvcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, cvc: e.target.value }))
    if (serverError) setServerError(null)
  }

  const handleCardSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setCardValidationError(null)
    setServerError(null)

    if (meta.error) {
      const errorMessage =
        meta.erroredInputs.cardNumber ||
        meta.erroredInputs.expiryDate ||
        meta.erroredInputs.cvc ||
        "Erreur de validation de la carte."
      setCardValidationError(errorMessage)
      toast.warning(errorMessage)
      return
    }

    if (!meta.cardType || !meta.cardType.type) {
      const message = "Type de carte non détecté. Veuillez vérifier le numéro."
      setCardValidationError(message)
      toast.warning(message)
      return
    }

    if (!isSupportedLibraryCardType(meta.cardType.type)) {
      const message = errorMessagesFr.unsupportedCardType
      setCardValidationError(message)
      toast.error(message)
      return
    }

    const targetCardType = mapLibraryTypeToTargetType(meta.cardType.type)

    setIsLoading(true)
    try {
      const formattedExpiryDate = cardData.expiryDate.replace(/\s*\/\s*/, "")
      const formattedCardNumber = cardData.cardNumber.replace(/\s/g, "")

      await registerCard({
        cardNumber: formattedCardNumber,
        cardExpirationDate: formattedExpiryDate,
        cardCvx: cardData.cvc,
        cardType: targetCardType,
      })

      toast.success("Carte enregistrée avec succès.")
      router.push(`/subscription/checkout?planId=${planId}&period=${period}`)
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erreur lors de l'enregistrement de la carte."
      setServerError(message)
      toast.error(message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Tabs
      selectedKey={step}
      onSelectionChange={(key) => {
        if (key === "card" && !hasMangopayAccount && step === "user") {
          toast.info("Veuillez d'abord enregistrer vos informations personnelles.")
          return
        }
        if (key === "user" && hasMangopayAccount) {
          return
        }
        setStep(key as "user" | "card")
        clearValidationErrors()
      }}
      aria-label="Étapes de configuration de l'abonnement"
      className="mt-4 max-w-full overflow-x-auto"
    >
      {!hasMangopayAccount && (
        <Tab key="user" title="1. Informations personnelles">
          <Card className="mt-4">
            <CardHeader>
              <h2 className="text-xl font-semibold">Vos informations</h2>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleUserSubmit} className="space-y-4" noValidate>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Input
                    label="Prénom"
                    name="firstName"
                    value={userData.firstName}
                    onChange={handleUserInputChange}
                    isRequired
                    variant="bordered"
                    aria-label="Prénom"
                    isInvalid={!!userFormErrors.firstName}
                    errorMessage={userFormErrors.firstName}
                  />
                  <Input
                    label="Nom"
                    name="lastName"
                    value={userData.lastName}
                    onChange={handleUserInputChange}
                    isRequired
                    variant="bordered"
                    aria-label="Nom"
                    isInvalid={!!userFormErrors.lastName}
                    errorMessage={userFormErrors.lastName}
                  />
                </div>
                <Input
                  label="Email"
                  name="email"
                  type="email"
                  defaultValue={session.data?.user.email || ""}
                  value={userData.email}
                  onChange={handleUserInputChange}
                  isRequired
                  variant="bordered"
                  aria-label="Email"
                  isInvalid={!!userFormErrors.email}
                  errorMessage={userFormErrors.email}
                />
                <Input
                  label="Adresse (ligne 1)"
                  name="addressLine1"
                  value={userData.addressLine1}
                  onChange={handleUserInputChange}
                  isRequired
                  variant="bordered"
                  aria-label="Adresse ligne 1"
                  isInvalid={!!userFormErrors.addressLine1}
                  errorMessage={userFormErrors.addressLine1}
                />
                <Input
                  label="Complément d'adresse (optionnel)"
                  name="addressLine2"
                  value={userData.addressLine2 ?? ""}
                  onChange={handleUserInputChange}
                  variant="bordered"
                  aria-label="Complément d'adresse"
                  isInvalid={!!userFormErrors.addressLine2}
                  errorMessage={userFormErrors.addressLine2}
                />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Input
                    label="Ville"
                    name="city"
                    value={userData.city}
                    onChange={handleUserInputChange}
                    isRequired
                    variant="bordered"
                    aria-label="Ville"
                    isInvalid={!!userFormErrors.city}
                    errorMessage={userFormErrors.city}
                  />
                  <Input
                    label="Code postal"
                    name="postalCode"
                    value={userData.postalCode}
                    onChange={handleUserInputChange}
                    isRequired
                    variant="bordered"
                    aria-label="Code postal"
                    isInvalid={!!userFormErrors.postalCode}
                    errorMessage={userFormErrors.postalCode}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <CountryDropdown
                    name="country"
                    labelType="full"
                    valueType="short"
                    whitelist={WHITE_LISTED_COUNTRIES}
                    value={userData.country}
                    onChange={(_, e) => {
                      setUserData((prev) => ({ ...prev, region: "" }))
                      handleUserInputChange(e)
                    }}
                    // @ts-expect-error Binding types error
                    customRender={CustomRender}
                    customProps={{
                      label: "Pays",
                      name: "country",
                      isRequired: true,
                      variant: "bordered",
                      ariaLabel: "Pays",
                      isInvalid: !!userFormErrors.country,
                      errorMessage: userFormErrors.country,
                    }}
                  />
                  <RegionDropdown
                    name="region"
                    blankOptionLabel="Aucun pays sélectionné..."
                    defaultOptionLabel="Région"
                    valueType="full"
                    countryValueType="short"
                    disableWhenEmpty={true}
                    country={userData.country}
                    value={userData.region}
                    onChange={(_, e) => {
                      handleUserInputChange(e)
                    }}
                    // @ts-expect-error Binding types error
                    customRender={CustomRender}
                    customProps={{
                      label: "Région",
                      name: "region",
                      isRequired: true,
                      variant: "bordered",
                      ariaLabel: "Région",
                      isInvalid: !!userFormErrors.region,
                      errorMessage: userFormErrors.region,
                    }}
                  />
                </div>
                {serverError && step === "user" && <p className="text-sm text-danger">{serverError}</p>}
                <div className="mt-6 flex justify-end">
                  <Button color="primary" type="submit" isLoading={isLoading} size="lg">
                    Continuer vers le paiement
                  </Button>
                </div>
              </form>
            </CardBody>
          </Card>
        </Tab>
      )}

      <Tab key="card" title={hasMangopayAccount ? "1. Carte de paiement" : "2. Carte de paiement"}>
        <Card className="mt-4">
          <CardHeader>
            <h2 className="text-xl font-semibold">Informations de paiement</h2>
          </CardHeader>
          <CardBody>
            <form onSubmit={handleCardSubmit} className="space-y-4" noValidate>
              {meta.cardType && (
                <p
                  className={cn(
                    "mb-2 text-sm",
                    meta.cardType.type && !isSupportedLibraryCardType(meta.cardType.type)
                      ? "text-warning"
                      : "text-default-600"
                  )}
                >
                  Type de carte détecté : {meta.cardType.displayName}
                  {meta.cardType.type && !isSupportedLibraryCardType(meta.cardType.type) && " (Non supporté)"}
                </p>
              )}

              <Input
                {...getCardNumberProps({ onChange: handleCardNumberChange, value: cardData.cardNumber })}
                label="Numéro de carte"
                placeholder="0000 0000 0000 0000"
                isRequired
                variant="bordered"
                startContent={CardIcon}
                isInvalid={
                  (meta.touchedInputs?.cardNumber && !!meta.erroredInputs?.cardNumber) || !!cardValidationError
                }
                errorMessage={(meta.touchedInputs?.cardNumber && meta.erroredInputs.cardNumber) || cardValidationError}
                aria-label="Numéro de carte"
                className="mb-4"
              />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <Input
                  //@ts-expect-error type of import not found
                  {...getExpiryDateProps({ onChange: handleExpiryDateChange, value: cardData.expiryDate })}
                  label="Date d'expiration"
                  placeholder="MM / YY"
                  isRequired
                  variant="bordered"
                  isInvalid={meta.touchedInputs?.expiryDate && !!meta.erroredInputs?.expiryDate}
                  errorMessage={meta.touchedInputs?.expiryDate && meta.erroredInputs.expiryDate}
                  aria-label="Date d'expiration MM/YY"
                />

                <Input
                  //@ts-expect-error type of import not found
                  {...getCVCProps({ onChange: handleCvcChange, value: cardData.cvc })}
                  label="CVC"
                  placeholder="123"
                  isRequired
                  variant="bordered"
                  isInvalid={meta.touchedInputs?.cvc && !!meta.erroredInputs?.cvc}
                  errorMessage={meta.touchedInputs?.cvc && meta.erroredInputs.cvc}
                  aria-label="Code CVC de sécurité"
                />
              </div>

              {serverError && step === "card" && <p className="mt-2 text-sm text-danger">{serverError}</p>}

              <div className={cn("mt-6 flex", !hasMangopayAccount ? "justify-between" : "justify-end")}>
                {!hasMangopayAccount && (
                  <Button variant="light" onPress={() => setStep("user")} disabled={isLoading}>
                    Retour
                  </Button>
                )}
                <Button
                  color="primary"
                  type="submit"
                  isLoading={isLoading}
                  size="lg"
                  isDisabled={isLoading || !!meta.error || !!cardValidationError}
                >
                  Enregistrer et continuer
                </Button>
              </div>
            </form>
          </CardBody>
        </Card>
      </Tab>
    </Tabs>
  )
}
