"use server"

import React from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HelpCircle, Settings2 } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"

function getDisplayReason(reason?: string | null): string {
  if (!reason) return "Une erreur inconnue est survenue."

  if (reason.includes("PSP timeout please try later")) {
    return "Le prestataire de paiement est temporairement indisponible. Veuillez réessayer plus tard."
  }

  if (reason.includes("PSP configuration error")) {
    return "Erreur de configuration du prestataire de paiement."
  }

  if (reason.includes("PSP technical error")) {
    return "Une erreur technique est survenue lors du paiement. Veuillez réessayer."
  }

  if (reason.includes("Bank technical error")) {
    return "La banque a rencontré une erreur technique. Veuillez réessayer plus tard."
  }

  if (reason.includes("Transaction refused by the bank (Do not honor)")) {
    return "La banque a refusé la transaction. Veuillez contacter votre banque."
  }

  if (reason.includes("Maximum number of attempts reached")) {
    return "Nombre maximal de tentatives atteint. Veuillez réessayer plus tard."
  }

  if (reason.includes("Transaction refused")) {
    return "La transaction a été refusée par la banque."
  }
  if (reason.includes("The 3DSecure authentication has failed")) {
    return "La finalisation sécurisée a échoué. Veuillez réessayer."
  }
  return "Le paiement n'a pas pu être traité."
}

export default async function SubscriptionFailurePage({
  searchParams,
}: {
  searchParams?: { [key: string]: string | undefined }
}) {
  const reason = searchParams?.reason
  const displayReason = getDisplayReason(reason)

  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20 sm:px-6 lg:px-8">
      <Card className="w-full overflow-hidden border-t-4 border-danger shadow-xl">
        <CardHeader className="justify-center bg-danger-50 p-6 dark:bg-danger-900/20">
          <AlertTriangle className="size-16 text-danger" strokeWidth={1.5} />
        </CardHeader>
        <CardBody className="flex flex-col items-center p-8 text-center sm:p-12">
          <h1 className="mb-3 text-3xl font-semibold text-gray-800 dark:text-gray-100">Échec du paiement</h1>
          <p className="mb-6 text-lg font-medium text-danger-700 dark:text-danger-400">
            Votre abonnement n&apos;a pas pu être activé.
          </p>

          <div className="mb-10 max-w-md rounded-lg border border-danger-200 bg-danger-50 p-4 text-default-600 dark:border-danger-700 dark:bg-danger-900/30">
            <p className="mb-1 font-semibold">Raison de l&apos;échec :</p>
            <p className="text-sm">{displayReason}</p>
          </div>

          <p className="mb-6 text-default-600 dark:text-default-300">Que souhaitez-vous faire ?</p>

          <Divider className="mx-auto my-6 w-1/2" />

          <div className="mt-6 flex w-full flex-col gap-4 sm:w-auto sm:flex-row">
            {/* Lien pour vérifier/modifier les infos de paiement */}
            <Link href={"/profile"} passHref>
              <Button
                color="warning"
                variant="solid"
                startContent={<Settings2 size={18} />}
                className="w-full sm:w-auto"
              >
                Vérifier mes informations
              </Button>
            </Link>
            {/* Lien vers le support */}
            <Link href={"/support"} passHref>
              <Button variant="ghost" startContent={<HelpCircle size={18} />} className="w-full sm:w-auto">
                Contacter le support
              </Button>
            </Link>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
