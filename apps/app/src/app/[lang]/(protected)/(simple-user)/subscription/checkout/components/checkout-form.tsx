"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { AlertCircle } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { getBrowserInfo } from "@/lib/utils/browser-info"
import { But<PERSON> } from "@nextui-org/button"

export default function CheckoutForm({
  planId,
  period,
  cardId,
  mangopayCardId,
  price,
  periodText,
}: {
  planId: number
  period: "MONTHLY" | "ANNUAL"
  cardId: string
  mangopayCardId: string
  price: number
  periodText: string
}) {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  const createSubscription = trpc.subscription.create.useMutation({
    onSuccess: (data) => {
      setError(null)

      // --- CAS 1: Redirection 3DS nécessaire ---
      if (data.redirectUrl) {
        toast.info("Finalisation sécurisée en cours...", { autoClose: false, isLoading: true })
        setTimeout(() => {
          window.location.href = data.redirectUrl!
        }, 500) // Délai court
        return
      }

      toast.info("Traitement en cours...", { autoClose: false, isLoading: true })
      router.push(data.returnUrl || "/subscription/pending")
    },
    onError: (error) => {
      const errorMsg = error.message || "Une erreur de communication est survenue. Veuillez réessayer."
      setError(errorMsg)
    },
  })

  const handleConfirmSubscription = () => {
    setError(null)

    if (!mangopayCardId) {
      const msg = "Erreur: Impossible d'identifier la carte de paiement."
      setError(msg)
      console.error("Erreur critique: mangopayCardId manquant dans CheckoutForm")
      return
    }

    createSubscription.mutate({
      planId,
      billingPeriod: period,
      cardId: mangopayCardId,
      browserInfos: getBrowserInfo(),
    })
  }

  const formattedPrice = Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)

  return (
    <div className="mt-6">
      {error && (
        <div className="mb-4 flex w-full items-center gap-2 rounded-lg border border-danger-200 bg-danger-50 p-3">
          <AlertCircle className="size-5 shrink-0 text-danger" />
          <p className="text-sm text-danger">{error}</p>
        </div>
      )}

      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <Button
          variant="bordered"
          onPress={() => router.push("/pricing")}
          isDisabled={createSubscription.isPending}
          className="w-full sm:w-auto"
        >
          Annuler
        </Button>
        <Button
          color="primary"
          size="lg"
          onPress={handleConfirmSubscription}
          isLoading={createSubscription.isPending}
          isDisabled={!cardId || !mangopayCardId || createSubscription.isPending} // Désactiver pendant le chargement
          className="w-full px-8 font-semibold sm:w-auto"
        >
          {createSubscription.isPending ? "Traitement..." : `Payer ${formattedPrice}/${periodText}`}
        </Button>
      </div>
      <p className="mt-4 text-center text-xs text-default-500 sm:text-left">
        En cliquant sur &quot;Payer&quot;, vous acceptez nos{" "}
        <a href="/terms" target="_blank" className="underline hover:text-primary">
          Conditions Générales de Vente
        </a>
        .
      </p>
    </div>
  )
}
