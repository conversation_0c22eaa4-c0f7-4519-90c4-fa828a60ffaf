import React from "react"
import Link from "next/link"
import { redirect } from "next/navigation"
import { Loader2, XCircle } from "lucide-react"

import { prisma } from "@/lib/prisma"
import { getMangopayPayInDetails } from "@/lib/subscription"
import { MangopayRecurringPayIn } from "@/types/mangopay"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { PaymentStatus, SubscriptionStatus } from "@prisma/client"

// Fonction de gestion isolée pour permettre les redirections serveur
async function handleReturnLogic(searchParams: { [key: string]: string | string[] | undefined }): Promise<{
  status: "success" | "failure" | "pending" | "error" | "redirect"
  message?: string
  redirectLink?: string
}> {
  const mangopayTransactionId = searchParams?.transactionId as string
  const subscriptionId = searchParams?.subscriptionId as string
  const paymentId = searchParams?.paymentId as string

  logger.log("Subscription return page logic started", { mangopayTransactionId, subscriptionId, paymentId })

  if (!mangopayTransactionId || !subscriptionId || !paymentId) {
    logger.error("Missing parameters in return URL", { searchParams })
    return { status: "error", message: "Paramètres de retour invalides." }
  }

  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId, subscriptionId: subscriptionId },
    })

    if (!payment) {
      logger.error("Payment record not found for return URL params", { paymentId, subscriptionId })
      return { status: "error", message: "Enregistrement de paiement non trouvé." }
    }

    if (payment.status === PaymentStatus.SUCCEEDED) {
      logger.log("Payment already succeeded, redirecting to success", { paymentId })
      return { status: "redirect", redirectLink: "/subscription/success" }
    }
    if (payment.status === PaymentStatus.FAILED) {
      logger.log("Payment already failed, redirecting to failure", { paymentId })
      return {
        status: "redirect",
        redirectLink: `/subscription/failure${payment.failureReason ? `?reason=${encodeURIComponent(payment.failureReason.substring(0, 100))}` : ""}`,
      }
    }

    const payInDetails = (await getMangopayPayInDetails(mangopayTransactionId)) as MangopayRecurringPayIn
    if (!payInDetails) {
      logger.error("Failed to fetch PayIn details from MangoPay", { mangopayTransactionId })
      throw new Error("Impossible de vérifier le statut du paiement auprès de MangoPay.")
    }

    let finalPaymentStatus: PaymentStatus
    let finalSubscriptionStatus: SubscriptionStatus | undefined = undefined
    let failureReason: string | null = null
    let outcome: "success" | "failure" | "pending" = "pending"

    switch (payInDetails.Status) {
      case "SUCCEEDED":
        finalPaymentStatus = PaymentStatus.SUCCEEDED
        finalSubscriptionStatus = SubscriptionStatus.ACTIVE
        outcome = "success"
        logger.log("PayIn SUCCEEDED", { paymentId, subscriptionId, mangopayPayInId: payInDetails.Id })
        break
      case "FAILED":
        finalPaymentStatus = PaymentStatus.FAILED
        finalSubscriptionStatus = SubscriptionStatus.FAILED
        failureReason = `${payInDetails.ResultCode}: ${payInDetails.ResultMessage}` || "Paiement échoué via MangoPay"
        outcome = "failure"
        logger.warn("PayIn FAILED", {
          paymentId,
          subscriptionId,
          mangopayPayInId: payInDetails.Id,
          reason: failureReason,
        })
        break
      default:
        finalPaymentStatus = PaymentStatus.PENDING
        outcome = "pending"
        logger.log("PayIn status is PENDING/CREATED upon return", {
          paymentId,
          subscriptionId,
          status: payInDetails.Status,
        })
        break
    }

    // Mise à jour transactionnelle de la BDD
    await prisma.$transaction([
      prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: finalPaymentStatus,
          mangopayPayinId: payInDetails.Id,
          failureReason: failureReason,
        },
      }),
      ...(finalSubscriptionStatus
        ? [
            prisma.subscription.update({
              where: { id: subscriptionId },
              data: { status: finalSubscriptionStatus },
            }),
          ]
        : []),
    ])

    switch (outcome) {
      case "success":
        logger.log("Payment and subscription updated to SUCCEEDED/ACTIVE", { paymentId, subscriptionId })
        return { status: "redirect", redirectLink: "/subscription/success" }
      case "failure":
        logger.log("Payment and subscription updated to FAILED", { paymentId, subscriptionId })
        return {
          status: "redirect",
          redirectLink: `/subscription/failure${failureReason ? `?reason=${encodeURIComponent(failureReason.substring(0, 100))}` : ""}`,
        }
      case "pending":
        logger.log("Payment and subscription updated to PENDING", { paymentId, subscriptionId })
        return { status: "redirect", redirectLink: "/subscription/pending" }
    }
  } catch (error) {
    logger.error("Error processing subscription return", {
      // @ts-expect-error error of type any
      error: error?.message,
      mangopayTransactionId,
      subscriptionId,
      paymentId,
    })
    // Tentative de mise à jour DB en FAILED en cas d'erreur de traitement
    try {
      await prisma.$transaction([
        prisma.payment.update({
          where: { id: paymentId },
          data: {
            status: PaymentStatus.FAILED,
            // @ts-expect-error error of type any
            failureReason: `Return Processing Error: ${error?.message?.substring(0, 150)}`,
          },
        }),
        prisma.subscription.update({
          where: { id: subscriptionId },
          data: { status: SubscriptionStatus.FAILED },
        }),
      ])
    } catch (dbUpdateError) {
      logger.error("Failed to update DB status after return processing error", { dbUpdateError })
    }

    return { status: "error", message: "Une erreur technique est survenue lors de la confirmation." }
  }
}

export default async function SubscriptionReturnPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const result = await handleReturnLogic(searchParams)

  if (result?.status === "redirect" && result?.redirectLink) {
    redirect(result.redirectLink)
  }

  let icon = <Loader2 className="size-12 animate-spin text-primary" />
  let title = "Traitement de votre paiement..."
  let message = "Veuillez patienter pendant que nous confirmons le statut. Vous serez redirigé automatiquement."
  let showErrorDetails = false

  if (result?.status === "error") {
    icon = <XCircle className="size-12 text-danger" />
    title = "Erreur de traitement"
    message = result.message || "Une erreur inattendue est survenue."
    showErrorDetails = true
  }

  return (
    <div className="container mx-auto flex min-h-screen max-w-lg items-center justify-center py-20">
      <Card className="w-full shadow-lg">
        <CardHeader className="justify-center">
          <h1 className="text-center text-2xl font-semibold">{title}</h1>
        </CardHeader>
        <CardBody className="flex flex-col items-center gap-6 px-6 py-8 text-center">
          <div className="mb-4">{icon}</div>
          <p className="text-default-600">{message}</p>
          {showErrorDetails && (
            <p className="mt-2 text-sm text-default-500">Si le problème persiste, veuillez contacter le support.</p>
          )}
          {/* Lien visible seulement en cas d'erreur pour aider l'utilisateur */}
          {showErrorDetails && (
            <Link href="/dashboard" passHref>
              <Button color="primary" variant="ghost" className="mt-6">
                Aller au tableau de bord
              </Button>
            </Link>
          )}
          {!showErrorDetails && (
            <p className="mt-4 text-sm text-default-500">Si vous n&apos;êtes pas redirigé, cliquez ci-dessous.</p>
          )}
          {/* Lien visible si pas d'erreur, au cas où la redirection auto échouerait */}
          {!showErrorDetails && (
            <div className="mt-6 space-x-4">
              <Link href="/subscription/success" passHref>
                <Button variant="ghost">Succès (Test)</Button>
              </Link>
              <Link href="/subscription/failure" passHref>
                <Button variant="ghost">Échec (Test)</Button>
              </Link>
              <Link href="/subscription/pending" passHref>
                <Button variant="ghost">En attente (Test)</Button>
              </Link>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  )
}
