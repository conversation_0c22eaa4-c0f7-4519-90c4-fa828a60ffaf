import NavSettings from "@/components/nav-settings"
import { Locale } from "@/lib/i18n-config"

export default async function ProtectedLayout({
  children,
  params: { lang },
}: {
  children: React.ReactNode
  params: {
    lang: Locale
  }
}) {
  return (
    <>
      <NavSettings lang={lang} />
      <div className="mx-auto w-full max-w-[1200px] grow space-y-2 p-4 sm:px-6 md:px-8">{children}</div>
    </>
  )
}
