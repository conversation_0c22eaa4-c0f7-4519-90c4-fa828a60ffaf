// app/[lang]/(protected)/agent/[idAgent]/page.tsx
import React from "react"

import { getUserSavedChats } from "@/actions/chat-actions"
import AgentDescription from "@/components/AgentDescription"
import Chatbox from "@/components/chatbox"
import { ChatProvider } from "@/contexts/ChatContext"
import { serverTrpc } from "@/lib/trpc/server"
import { SavedChat } from "@/types/chat"

export default async function AgentPage({
  params: { idAgent },
}: {
  params: { idAgent: string }
  searchParams?: { chatId?: string }
}) {
  const agent = await serverTrpc.agent.getById(Number(idAgent))

  // Fetch saved chats server-side
  const savedChats = (await getUserSavedChats(Number(idAgent))) as SavedChat[]

  if (!agent) {
    return <div>Hum! Quel<PERSON> chose s&lsquo;est mal passé lors de la récupération de l&lsquo;agent</div>
  }

  return (
    <ChatProvider agentId={agent.id}>
      <div className="flex size-full flex-row">
        <AgentDescription agent={agent} savedChats={savedChats} />
        <div className="flex-1 overflow-hidden">
          <Chatbox agent={agent} />
        </div>
      </div>
    </ChatProvider>
  )
}
