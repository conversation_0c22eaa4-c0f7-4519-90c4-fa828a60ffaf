// agent/[idAgent]/layout.tsx
import React from "react"
import { LucideHome } from "lucide-react"

import CustomB<PERSON>crumbs, { CustomBreadcrumbItemProps } from "@/components/custom-breadcrumbs"
import { AgentProvider } from "@/contexts/AgentContext"
import { serverTrpc } from "@/lib/trpc/server"

const AgentLayout = async ({
  children,
  params: { idAgent },
}: {
  children: React.ReactNode
  params: { idAgent: string }
}) => {
  const agent = await serverTrpc.agent.getById(Number(idAgent))

  if (!agent)
    return (
      <div className="mx-auto w-full max-w-[1200px] p-4 sm:px-6 md:px-8">
        <div className="rounded-lg border p-6 text-center shadow-md">
          Que<PERSON><PERSON> chose s&lsquo;est mal passé lors de la récupération de l&lsquo;agent
        </div>
      </div>
    )

  const breadcrumbItems: CustomBreadcrumbItemProps[] = [
    { href: "/", icon: <LucideHome /> },
    { href: "", label: `Agent ${agent.title}` },
  ]

  return (
    <AgentProvider agent={agent}>
      <CustomBreadcrumbs items={breadcrumbItems} />
      <div className="flex h-[calc(100vh-180px)] w-full overflow-hidden rounded-lg border shadow-md">{children}</div>
    </AgentProvider>
  )
}

export default AgentLayout
