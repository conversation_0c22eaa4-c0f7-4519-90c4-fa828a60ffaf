import DeleteAccountButton from "@/components/auth/delete-account-button"
import { DeleteAccountButtonDr } from "@/components/auth/delete-account-button.dr"
import SignoutButton from "@/components/auth/sign-out-button"
import VerifyEmailButton from "@/components/auth/verify-email-button"
import { VerifyEmailButtonDr } from "@/components/auth/verify-email-button.dr"
import ChangePasswordForm from "@/components/profile/change-password-form"
import UserPaymentCards from "@/components/profile/payment/user-payment-cards"
import PlanLimitsInfo from "@/components/profile/PlanLimitsInfo"
import ProfileDetails from "@/components/profile/profile-details"
import { ProfileDetailsDr } from "@/components/profile/profile-details.dr"
import UserActiveSessions from "@/components/profile/sessions/user-active-sessions"
import { UserActiveSessionsDr } from "@/components/profile/sessions/user-active-sessions.dr"
import SubscriptionManagement from "@/components/subscriptions/client/SubscriptionManagement"
import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { dictionaryRequirements } from "@/lib/utils/dictionary"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"

export default async function Profile({
  params: { lang },
}: {
  params: {
    lang: Locale
  }
}) {
  const dictionary = await getDictionary(
    lang,
    dictionaryRequirements(
      {
        profile: true,
        profilePage: {
          serverSideData: true,
        },
        deleteAccount: true,
        signOut: true,
      },
      UserActiveSessionsDr,
      DeleteAccountButtonDr,
      VerifyEmailButtonDr,
      ProfileDetailsDr
    )
  )
  const session = await auth()

  const hasVerifiedEmail = Boolean(session?.user.emailVerified)

  return (
    <main className="container m-auto flex flex-1 flex-col items-center p-6 pb-20">
      <div className="w-full max-w-5xl space-y-8">
        <h1 className="mb-6 text-3xl font-bold">Mon Profil</h1>

        {/* Section Informations Personnelles */}
        <Card className="w-full">
          <CardHeader className="flex justify-between flex-wrap">
            <h2 className="text-xl font-semibold">Informations Personnelles</h2>
            <div className="flex items-center space-x-2">
              {session && <VerifyEmailButton session={session} dictionary={dictionary} />}
            </div>
          </CardHeader>
          <CardBody>
            <ProfileDetails dictionary={dictionary} hasVerifiedEmail={hasVerifiedEmail} />
          </CardBody>
        </Card>

        {/* Section Sécurité */}
        <Card className="w-full">
          <CardHeader>
            <h2 className="text-xl font-semibold">Sécurité</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="mb-2 text-lg font-medium">Mot de passe</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Modifiez votre mot de passe pour sécuriser votre compte
                </p>
                <ChangePasswordForm />
              </div>

              <Divider />

              <div>
                <h3 className="mb-2 text-lg font-medium">Sessions actives</h3>
                <UserActiveSessions dictionary={dictionary} />
              </div>
            </div>
          </CardBody>
        </Card>

        <PlanLimitsInfo />

        {/* Section Abonnement */}
        <SubscriptionManagement />

        {/* Section Paiement */}
        {session?.user.mangopayUserId && (
          <Card className="w-full">
            <CardHeader>
              <h2 className="text-xl font-semibold">Méthodes de paiement</h2>
            </CardHeader>
            <CardBody>
              <p className="mb-4 text-sm text-muted-foreground">
                Gérez vos cartes de paiement et vos informations de facturation
              </p>
              <UserPaymentCards />
            </CardBody>
          </Card>
        )}

        {/* Section Compte */}
        <Card className="w-full">
          <CardHeader>
            <h2 className="text-xl font-semibold">Paramètres du compte</h2>
          </CardHeader>
          <CardBody>
            <div className="flex flex-col space-y-4">
              {/* <p className="text-sm text-muted-foreground">{dictionary.profilePage.serverSideData}</p> */}

              <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-end md:space-x-2 md:space-y-0">
                <DeleteAccountButton dictionary={dictionary}>{dictionary.deleteAccount}</DeleteAccountButton>
                <SignoutButton>{dictionary.signOut}</SignoutButton>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </main>
  )
}
