"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON> } from "lucide-react"

import { Icons } from "@/components/icons"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON>, CardBody, CardFooter, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Link } from "@nextui-org/link"

import "../globals.css"

export default function ErrorPage({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  const [errorMessage, setErrorMessage] = useState<string>("Une erreur inattendue s'est produite.")
  const [errorCode, setErrorCode] = useState<string>("")

  useEffect(() => {
    // Capture l'erreur pour l'afficher à l'utilisateur
    console.error("Application error:", error)

    if (error.message) {
      setErrorMessage(error.message)
    }

    if (error.digest) {
      setErrorCode(error.digest)
    }
  }, [error])

  return (
    <main className="container m-auto flex min-h-screen flex-1 flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md border border-foreground/10 bg-background/60 backdrop-blur-md">
        <CardHeader className="flex flex-col items-center gap-2 pb-0">
          <div className="flex w-full justify-center">
            <Icons.logo className="size-16 text-primary" />
          </div>
          <h1 className="text-center text-2xl font-bold">Oups, quelque chose s&apos;est mal passé</h1>
        </CardHeader>
        <CardBody className="flex flex-col items-center py-8">
          <div className="mb-6 flex justify-center">
            <div className="rounded-full bg-danger/10 p-4">
              <AlertTriangle className="size-12 text-danger" />
            </div>
          </div>
          <p className="mb-4 text-center text-foreground-500">{errorMessage}</p>
          {errorCode && (
            <>
              <Divider className="my-4" />
              <div className="rounded bg-foreground-100/20 px-2 py-1 font-mono text-xs text-foreground-400">
                Code d&apos;erreur: {errorCode}
              </div>
            </>
          )}
        </CardBody>
        <CardFooter className="flex flex-col gap-3 pb-6">
          <Button
            onPress={reset}
            color="primary"
            variant="flat"
            startContent={<RefreshCw size={18} />}
            className="w-full px-6 font-medium"
          >
            Réessayer
          </Button>
          <Button as={Link} href="/" color="default" variant="light" className="font-medium">
            Retour à l&apos;accueil
          </Button>
        </CardFooter>
      </Card>
    </main>
  )
}
