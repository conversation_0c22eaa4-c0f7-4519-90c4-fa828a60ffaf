import { Metada<PERSON> } from "next"
import { redirect } from "next/navigation"

import { authRoutes } from "@/constants/auth"
import { auth } from "@/lib/auth"
import { env } from "@/lib/env"

export const metadata: Metadata = {
  title: "Sign-up",
  description: "Create an account",
}

export default async function SignupLayout({ children }: { children: React.ReactNode }) {
  if (env.DISABLE_REGISTRATION === true) {
    redirect(authRoutes.signIn[0])
  }
  const session = await auth()
  if (session?.user) {
    redirect("/")
  }
  return <>{children}</>
}
