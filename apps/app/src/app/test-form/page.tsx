"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ody, CardHeader } from "@nextui-org/card"
import { <PERSON><PERSON> } from "@nextui-org/button"

import { trpc } from "@/lib/trpc/client"
import { DynamicForm } from "@/components/forms/dynamic-form"

export default function TestFormPage() {
  const [showForm, setShowForm] = React.useState(false)
  const { data: cancellationForm, isLoading, error } = trpc.subscription.getCancellationForm.useQuery()

  const handleFormSubmissionComplete = (submissionId: string) => {
    console.log("Form submitted with ID:", submissionId)
    setShowForm(false)
  }

  const handleFormCancel = () => {
    setShowForm(false)
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">Test Form Display</h1>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Form Query Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-3 bg-gray-100 rounded">
                <strong>Loading:</strong> {isLoading ? "Yes" : "No"}
              </div>
              <div className="p-3 bg-gray-100 rounded">
                <strong>Error:</strong> {error ? error.message : "None"}
              </div>
              <div className="p-3 bg-gray-100 rounded">
                <strong>Form Found:</strong> {cancellationForm ? "Yes" : "No"}
              </div>
            </div>
          </div>

          {cancellationForm && (
            <div className="space-y-2">
              <h2 className="text-lg font-semibold">Form Details</h2>
              <div className="p-4 bg-blue-50 rounded">
                <p><strong>Title:</strong> {cancellationForm.title}</p>
                <p><strong>Description:</strong> {cancellationForm.description || "None"}</p>
                <p><strong>Questions:</strong> {cancellationForm.questions?.length || 0}</p>
                <p><strong>Status:</strong> {cancellationForm.status}</p>
                <p><strong>Active:</strong> {cancellationForm.isActive ? "Yes" : "No"}</p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <Button 
              color="primary" 
              onPress={() => setShowForm(true)}
              isDisabled={!cancellationForm}
            >
              {cancellationForm ? "Show Form" : "No Form Available"}
            </Button>

            {showForm && cancellationForm && (
              <div className="mt-6">
                <h2 className="text-lg font-semibold mb-4">Form Preview</h2>
                <DynamicForm
                  formType="SUBSCRIPTION_CANCELLATION"
                  onSubmissionComplete={handleFormSubmissionComplete}
                  onCancel={handleFormCancel}
                  className="border border-gray-200 rounded-lg"
                />
              </div>
            )}
          </div>

          <div className="mt-6 p-4 bg-yellow-50 rounded">
            <h3 className="font-semibold">Debug Information</h3>
            <pre className="text-sm mt-2 overflow-auto">
              {JSON.stringify({ 
                hasForm: !!cancellationForm,
                formId: cancellationForm?.id,
                questionsCount: cancellationForm?.questions?.length,
                isLoading,
                error: error?.message 
              }, null, 2)}
            </pre>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
