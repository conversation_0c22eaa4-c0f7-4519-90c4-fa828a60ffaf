"use client"

import { <PERSON><PERSON><PERSON>, SetStateAction, useState } from "react"
import { toast } from "react-toastify"

import { Button } from "@nextui-org/button"
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader } from "@nextui-org/modal"

interface OptimizePromptButtonProps {
  currentPrompt: string
  onKeep: Dispatch<SetStateAction<string>>
}

export default function OptimizePromptButton({ currentPrompt, onKeep }: OptimizePromptButtonProps) {
  const [showModal, setShowModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [optimizedPrompt, setOptimizedPrompt] = useState("")

  const handleOptimize = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/optimize-prompt", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt: currentPrompt }),
      })

      const data = await response.json()
      setOptimizedPrompt((data as { optimizedPrompt: string }).optimizedPrompt)
    } catch (error) {
      console.error("Erreur lors de l’optimisation du prompt:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleKeep = () => {
    if (!optimizedPrompt) return
    if (onKeep) onKeep(optimizedPrompt)
    setShowModal(false)
  }

  const handleReject = () => {
    setOptimizedPrompt("")
    setShowModal(false)
  }

  return (
    <>
      <Button
        color="secondary"
        onPress={() => {
          if (currentPrompt) setShowModal(true)
          else toast.error("Pas de prompt à optimiser")
        }}
      >
        Optimiser le prompt
      </Button>

      <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
        <ModalContent className="max-h-[90vh] lg:min-w-[600px]">
          <ModalHeader>
            <h2 className="text-lg font-bold">Optimisation du prompt</h2>
          </ModalHeader>
          <ModalBody className="max-h-[80%] overflow-y-auto">
            <div className="mt-4 overflow-y-auto rounded border p-4">
              <p className="whitespace-pre-wrap">{optimizedPrompt ? optimizedPrompt : currentPrompt}</p>
            </div>
          </ModalBody>
          <ModalFooter>
            {optimizedPrompt ? (
              <>
                <Button variant="solid" onPress={handleReject}>
                  Rejeter
                </Button>
                <Button color="primary" onPress={handleKeep}>
                  Conserver
                </Button>
              </>
            ) : (
              <Button isLoading={loading} onPress={handleOptimize} className="mt-4">
                Lancer l&apos;optimisation
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
