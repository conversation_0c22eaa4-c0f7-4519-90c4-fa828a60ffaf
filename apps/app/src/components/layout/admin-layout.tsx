import React from "react"

import { Locale } from "@/lib/i18n-config"

import { AdminNavbarWrapper } from "../navbar/admin-navbar"
import { ServerSidebarProvider } from "../sidebar/server-sidebar-provider"

import { AdminLayoutClient } from "./admin-layout-client"

interface Props {
  children: React.ReactNode
  lang: Locale
}

export const Layout = ({ children, lang }: Props) => {
  return (
    <AdminLayoutClient>
      <section className="flex">
        <ServerSidebarProvider />
        <AdminNavbarWrapper lang={lang}>{children}</AdminNavbarWrapper>
      </section>
    </AdminLayoutClient>
  )
}
