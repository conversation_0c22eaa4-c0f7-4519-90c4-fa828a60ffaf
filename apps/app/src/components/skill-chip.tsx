// File: /components/SkillChip.tsx
import React from "react"
import { XIcon } from "lucide-react"

interface SkillChipProps {
  label: string
  onRemove: () => void
  isNew: boolean
}

const SkillChip: React.FC<SkillChipProps> = ({ label, onRemove }) => {
  return (
    <div className="group inline-flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
      <span>{label}</span>
      <button
        type="button"
        onClick={onRemove}
        className="inline-flex size-4 items-center justify-center rounded-full text-blue-600 transition-colors hover:bg-blue-200 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
        aria-label={`Remove ${label}`}
      >
        <XIcon size={12} />
      </button>
    </div>
  )
}

export default SkillChip
