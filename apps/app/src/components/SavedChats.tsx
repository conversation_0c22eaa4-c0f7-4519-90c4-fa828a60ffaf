// components/SavedChats.tsx
"use client"

import React from "react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Clock, MessageSquare, Trash2 } from "lucide-react"

import { useChatContext } from "@/contexts/ChatContext"
import { SavedChat } from "@/types/chat"
import { Button } from "@nextui-org/button"
import { Divider } from "@nextui-org/divider"
import { ScrollShadow } from "@nextui-org/scroll-shadow"
import { Tooltip } from "@nextui-org/tooltip"

interface SavedChatsProps {
  savedChats: SavedChat[]
}

const SavedChats: React.FC<SavedChatsProps> = ({ savedChats }) => {
  const { chatId, setChatId, deleteChat } = useChatContext()

  const handleChatSelect = (id: string): void => {
    setChatId(id)
    // window.location.href = `?chatId=${id}`
  }

  const handleDeleteChat = async (e: React.MouseEvent, id: string): Promise<void> => {
    e.stopPropagation() // Prevent chat selection when clicking delete
    await deleteChat(id)
  }

  if (savedChats.length === 0) {
    return null
  }

  return (
    <div className="mt-4 w-full">
      <div className="mb-2 flex items-center gap-2">
        <MessageSquare className="size-4" />
        <h3 className="text-md font-semibold">Conversations sauvegardées</h3>
      </div>

      <Divider className="my-2" />

      <ScrollShadow className="h-[200px]">
        <div className="space-y-2">
          {savedChats.map((chat) => (
            <div key={chat.id} className="flex items-center gap-2">
              <Tooltip content={chat.title || "Sans titre"}>
                <Button
                  variant={chatId === chat.id ? "solid" : "light"}
                  color={chatId === chat.id ? "primary" : "default"}
                  className="w-full justify-start text-left"
                  onPress={() => handleChatSelect(chat.id)}
                >
                  <div className="w-full truncate">
                    <div className="truncate font-medium">{chat.title || "Sans titre"}</div>
                    <div className="flex items-center gap-1 text-xs text-default-700">
                      <Clock className="size-3" />
                      {format(new Date(chat.updatedAt), "d MMM yyyy à HH:mm", { locale: fr })}
                    </div>
                  </div>
                </Button>
              </Tooltip>

              <Tooltip content="Supprimer la conversation">
                <Button
                  isIconOnly
                  variant="light"
                  color="danger"
                  size="sm"
                  onClick={(e) => handleDeleteChat(e as React.MouseEvent, chat.id)}
                  aria-label="Supprimer la conversation"
                >
                  <Trash2 className="size-4" />
                </Button>
              </Tooltip>
            </div>
          ))}
        </div>
      </ScrollShadow>
    </div>
  )
}

export default SavedChats
