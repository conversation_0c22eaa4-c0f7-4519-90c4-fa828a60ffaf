"use client"

import React, { ChangeEvent, useState } from "react"
import { UserPlus2 } from "lucide-react"
import { toast } from "react-toastify"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { But<PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"

// Schéma de validation Zod pour les données du formulaire
const skillCreateSchema = z.object({
  name: z.string().min(1, "Le nom est requis"),
  agentId: z.number().optional(),
})

// Type pour les données du formulaire
type FormData = z.infer<typeof skillCreateSchema>

// Props du composant
interface CreateSkillModalProps {
  onSkillCreated?: () => void
}

const CreateSkillModal: React.FC<CreateSkillModalProps> = ({ onSkillCreated }) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState<boolean>(false)
  const [formData, setFormData] = useState<FormData>({
    name: "",
    agentId: undefined,
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const skillCreate = trpc.skill.create.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Skill créé avec succès!")
      utils.user.getAll.invalidate()
      // Réinitialisation du formulaire
      setFormData({
        name: "",
      })
      setErrors({})
      if (onSkillCreated) {
        onSkillCreated()
      }
    },
    onError: (error) => {
      logger.log(error)
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
    // Réinitialisation du formulaire
    setFormData({
      name: "",
      agentId: undefined,
    })

    setErrors({})
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    // Effacer l'erreur du champ modifié
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = () => {
    try {
      const validatedData = skillCreateSchema.parse({
        ...formData,
      })
      skillCreate.mutate(validatedData)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors = error.flatten().fieldErrors
        const errorMessages = Object.keys(fieldErrors).reduce(
          (acc, key) => {
            acc[key] = fieldErrors[key]?.join(", ") || ""
            return acc
          },
          {} as Record<string, string>
        )
        setErrors(errorMessages)
        toast.error("Veuillez corriger les erreurs dans le formulaire")
      }
    }
  }

  return (
    <>
      <Button
        color="primary"
        variant="flat"
        startContent={<UserPlus2 size={18} />}
        onPress={handleOpenModal}
        className="mb-4"
      >
        Créer un Skill
      </Button>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Nouvelle compétence</h2>
            <p className="text-sm text-default-500">Ajoutez une nouvelle compétence pour vos agents</p>
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit} className="flex flex-col gap-6">
              <Input
                label="Nom du skill"
                name="name"
                value={formData.name || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le nom complet"
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={skillCreate.isPending}
                labelPlacement="outside"
                isInvalid={!!errors.name}
                errorMessage={errors.name}
                classNames={{
                  label: "text-sm font-medium",
                }}
              />
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={skillCreate.isPending} onPress={handleSubmit} color="primary" variant="flat">
              Créer le skill
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default CreateSkillModal
