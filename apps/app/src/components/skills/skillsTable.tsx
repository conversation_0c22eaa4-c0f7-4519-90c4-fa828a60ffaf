"use client"
import React, { useCallback, useEffect, useState } from "react"
import { Search } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Prisma } from "@prisma/client"

import CreateSkillModal from "./newSkillModal"
import { RenderCell } from "./render-cell"
import { skillsColumns } from "./skills-colums"

type PaginationInfo = {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
}

interface SkillsTableProps {
  initialSkills: SkillType[]
  initialPagination: PaginationInfo
}
type SkillType = Prisma.SkillGetPayload<{
  include: {
    _count: {
      select: {
        agents: true
      }
    }
  }
}>

export const SkillsTable = ({ initialSkills, initialPagination }: SkillsTableProps) => {
  const [skills, setSkills] = useState<SkillType[]>(initialSkills)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [totalPages, setTotalPages] = useState(initialPagination.totalPages)
  const [filterValue, setFilterValue] = useState("")
  const [filterColumn, setFilterColumn] = useState("name")
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)

  const skillsQuery = trpc.skill.getAllForAdmin.useQuery({ page, pageSize }, { enabled: false })

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await skillsQuery.refetch()
      if (result.data) {
        setSkills(result.data.data)
        setTotalPages(result.data.pagination.totalPages)
      }
    } finally {
      setIsLoading(false)
    }
  }, [skillsQuery])

  const handleSkillsMutated = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  // Filtrage des utilisateurs
  const filteredSkills = React.useMemo(() => {
    if (!filterValue) return skills

    return skills.filter((skill) => {
      return skill.name?.toLowerCase().includes(filterValue.toLowerCase())
    })
  }, [skills, filterValue])

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex w-full flex-wrap justify-between gap-4">
        {/* Filtres */}
        <div className="mb-4 flex gap-4">
          <Input
            isClearable
            className="w-full sm:min-w-[200px] sm:max-w-[44%]"
            placeholder="Rechercher..."
            startContent={<Search className="text-default-300" size={18} />}
            value={filterValue}
            onClear={() => setFilterValue("")}
            onValueChange={setFilterValue}
          />
          <Select
            className="w-full sm:min-w-[150px] sm:max-w-[200px]"
            selectedKeys={[filterColumn]}
            onChange={(e) => setFilterColumn(e.target.value)}
            aria-label="Filtrer par"
          >
            <SelectItem key="name" value="name">
              Nom
            </SelectItem>
          </Select>
        </div>

        <CreateSkillModal onSkillCreated={handleSkillsMutated} />
      </div>

      {/* Table */}
      <Table
        aria-label="Tableau des utilisateurs"
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          {skillsColumns.map((column) => (
            <TableColumn
              key={column.uid}
              hideHeader={column.uid === "actions"}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody
          items={filteredSkills}
          loadingContent={<Spinner label="Chargement..." />}
          loadingState={isLoading ? "loading" : "idle"}
          emptyContent="Aucun utilisateur trouvé"
        >
          {(skill) => (
            <TableRow key={skill.id}>
              {(columnKey) => (
                <TableCell>
                  {RenderCell({
                    skill: skill,
                    columnKey: columnKey.toString(),
                    onMutation: handleSkillsMutated,
                  })}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
