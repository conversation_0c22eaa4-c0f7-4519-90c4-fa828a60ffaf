"use client"

import React, { useEffect, useRef, useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, RefreshCw, Square, User } from "lucide-react"

import { useChat } from "@ai-sdk/react"
import { Avatar } from "@nextui-org/avatar"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardFooter, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Input } from "@nextui-org/input"
import { Tooltip } from "@nextui-org/tooltip"
import { Agent } from "@prisma/client"

import { Icons } from "./icons"

interface AdminChatboxProps {
  agent: Agent
}

const AdminChatbox: React.FC<AdminChatboxProps> = ({ agent }) => {
  const { messages, setMessages, input, handleInputChange, handleSubmit, status, stop } = useChat({
    body: {
      agentId: agent.id,
    },
  })
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const formRef = useRef<HTMLFormElement>(null)
  const [userHasScrolled, setUserHasScrolled] = useState(false)
  const [isGenuinelyNewChat, setIsGenuinelyNewChat] = useState(true)

  // Fonction pour détecter si l'utilisateur a scrollé vers le haut
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget
    const isAtBottom = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) < 50
    setUserHasScrolled(!isAtBottom)
  }

  // Auto-scroll to bottom
  useEffect(() => {
    if (!userHasScrolled && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth", block: "end" })
    }
  }, [messages, userHasScrolled])

  // Simple way to detect waiting for AI's first response
  const isWaitingForAIResponse =
    messages.length === 1 && // Has only empty user message
    messages[0].role === "user" && // First message is from user
    messages[0].content === "" && // First message is empty
    status !== "streaming" // Not currently streaming

  // Réinitialiser la conversation
  const handleResetChat = () => {
    setMessages([])
    setIsGenuinelyNewChat(true)
  }

  const handleStartChat = () => {
    handleSubmit(new Event("submit") as Event, {
      allowEmptySubmit: true,
    })
  }

  const showStartButton = isGenuinelyNewChat && messages.length === 0

  return (
    <Card className="flex h-[600px] flex-col overflow-hidden">
      <CardHeader className="flex items-center justify-between border-b px-4 py-3">
        <div className="flex items-center gap-3">
          <Avatar
            icon={<Bot size={20} />}
            size="sm"
            classNames={{
              base: "bg-primary-100 text-primary",
            }}
          />
          <div>
            <p className="font-medium">Test de l&apos;agent</p>
            <p className="text-xs text-default-500">
              {agent.model} • Temp: {agent.temperature}
            </p>
          </div>
        </div>
        <Tooltip content="Nouvelle conversation">
          <Button isIconOnly variant="light" onPress={handleResetChat} aria-label="Nouvelle conversation" size="sm">
            <RefreshCw size={18} />
          </Button>
        </Tooltip>
      </CardHeader>

      {/* Chat Messages - Utiliser un div normal avec les classes de CardBody */}
      <div
        className={"grow space-y-4 overflow-y-auto p-4 " + (showStartButton ? "overflow-y-hidden" : "overflow-y-auto")}
        ref={messagesContainerRef}
        onScroll={handleScroll}
        style={{ height: "calc(100% - 120px)" }} // Hauteur fixe pour permettre le défilement
      >
        {showStartButton ? (
          <div className="flex h-full flex-col items-center justify-center text-center text-default-400">
            <Bot size={40} strokeWidth={1.5} className="mb-4 opacity-50" />
            <p className="mb-1 text-lg font-medium">Testez l&apos;agent en direct</p>
            <p className="max-w-md text-sm">Cliquez sur le bouton pour commencer la conversation avec l&apos;agent.</p>
            <Button color="primary" size="lg" onPress={handleStartChat} className="mt-4 px-8">
              Commencer
            </Button>
          </div>
        ) : (
          <AnimatePresence initial={false}>
            {messages.map((msg, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"}`}
              >
                {msg.content !== "" && (
                  <div className="flex max-w-[85%] gap-2">
                    {msg.role !== "user" && (
                      <Avatar
                        icon={<Bot size={14} />}
                        size="sm"
                        classNames={{
                          base: "h-6 w-6 bg-primary-100 text-primary self-start mt-1 flex-shrink-0",
                        }}
                      />
                    )}
                    <div
                      className={`shrink rounded-lg p-3 ${
                        msg.role === "user" ? "bg-primary text-white" : "bg-default-100 dark:bg-default-50/50"
                      }`}
                    >
                      <p className="whitespace-pre-wrap text-sm">{msg.content}</p>
                    </div>
                    {msg.role === "user" && (
                      <Avatar
                        icon={<User size={14} />}
                        size="sm"
                        classNames={{
                          base: "h-6 w-6 bg-secondary-100 text-secondary flex-shrink-0 self-start mt-1",
                        }}
                      />
                    )}
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        {isWaitingForAIResponse && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            className="flex justify-start"
          >
            <div className="flex max-w-[85%] gap-2">
              <Avatar
                icon={<Bot size={14} />}
                size="sm"
                classNames={{
                  base: "h-6 w-6 bg-primary-100 text-primary self-start mt-1",
                }}
              />
              <div className="rounded-lg bg-default-100 p-3 dark:bg-default-50/50">
                <Icons.typing />
              </div>
            </div>
          </motion.div>
        )}

        {!isWaitingForAIResponse && status === "streaming" && (
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className="flex justify-center">
            <Button
              type="button"
              variant="flat"
              color="danger"
              size="sm"
              onPress={() => stop()}
              startContent={<Square size={14} />}
            >
              Arrêter la génération
            </Button>
          </motion.div>
        )}

        {/* Élément invisible pour le scroll */}
        <div ref={messagesEndRef} />
      </div>

      <Divider />

      {/* Chat Input */}
      <CardFooter className="p-2">
        <form ref={formRef} className="flex w-full gap-2" onSubmit={handleSubmit}>
          <Input
            value={input}
            onChange={handleInputChange}
            autoComplete="off"
            placeholder={status === "streaming" ? "L'agent est en train d'écrire..." : "Envoyez un message..."}
            fullWidth
            disabled={status === "streaming" || showStartButton}
            variant="bordered"
            radius="full"
            classNames={{
              inputWrapper: "h-10",
            }}
            endContent={
              <Button
                type="submit"
                isIconOnly
                color="primary"
                variant={input.trim() ? "solid" : "flat"}
                isLoading={status === "streaming"}
                isDisabled={status === "streaming" || showStartButton || (!input.trim() && !isGenuinelyNewChat)}
                radius="full"
                size="sm"
              >
                {status !== "streaming" && <ArrowUp size={16} />}
              </Button>
            }
          />
        </form>
      </CardFooter>
    </Card>
  )
}

export default AdminChatbox
