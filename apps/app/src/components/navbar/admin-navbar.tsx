import React from "react"
import { Globe } from "lucide-react"

import { Locale } from "@/lib/i18n-config"
import { Link } from "@nextui-org/link"
import { Navbar, NavbarContent } from "@nextui-org/navbar"

import LocaleSwitcher from "../locale-switcher"
import { ThemeSwitch } from "../theme/theme-switch"

import { AdminBurgerButton } from "./admin-burger-button"

interface Props {
  children: React.ReactNode
  lang: Locale
}

export const AdminNavbarWrapper = ({ children, lang }: Props) => {
  return (
    <div className="relative flex flex-1 flex-col">
      <Navbar
        isBordered
        className="sticky left-0 top-0 w-full"
        classNames={{
          wrapper: "w-full max-w-full",
        }}
      >
        <NavbarContent className="md:hidden">
          <AdminBurgerButton />
        </NavbarContent>
        <NavbarContent className="w-full max-md:hidden">
          <Link href="/">
            <span className="sr-only">Accéder au site web</span>
            <Globe />
          </Link>
        </NavbarContent>
        <NavbarContent justify="end" className="w-fit data-[justify=end]:grow-0">
          <ThemeSwitch />

          <LocaleSwitcher lang={lang} />
        </NavbarContent>
      </Navbar>
      <div className="flex grow flex-col overflow-y-auto p-4">{children}</div>
    </div>
  )
}
