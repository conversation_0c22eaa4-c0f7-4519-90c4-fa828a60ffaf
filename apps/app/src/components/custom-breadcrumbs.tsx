"use client"

import { FC, ReactNode } from "react"

import { BreadcrumbItem, Breadcrumbs } from "@nextui-org/breadcrumbs"

// Définition du type des éléments du fil d'Ariane
export interface CustomBreadcrumbItemProps {
  label?: string
  href: string
  icon?: ReactNode
}

// Composant Breadcrumbs réutilisable
const CustomBreadcrumbs: FC<{ items: CustomBreadcrumbItemProps[] }> = ({ items }) => {
  return (
    <Breadcrumbs
      classNames={{
        list: "bg-gradient-to-br from-primary-600 to-primary-500 shadow-small p-2 rounded-md font-bold",
      }}
      itemClasses={{
        item: "text-white/70 data-[current=true]:text-white",
        separator: "text-white/40",
      }}
      underline="hover"
      variant="solid"
    >
      {items.map((item, index) => (
        <BreadcrumbItem key={index} href={item.href}>
          {item.icon || item.label}
        </BreadcrumbItem>
      ))}
    </Breadcrumbs>
  )
}

export default CustomBreadcrumbs
