// components/AgentDescription.tsx
"use client"

import React, { useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { SavedChat } from "@/types/chat"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Agent } from "@prisma/client"

import SavedChats from "./SavedChats"

interface AgentDescriptionProps {
  agent: Agent
  savedChats: SavedChat[]
}

const AgentDescription: React.FC<AgentDescriptionProps> = ({ agent, savedChats }) => {
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div
      className={`relative flex h-full flex-col border-r transition-all duration-300 ${
        isCollapsed ? "w-12" : "w-full md:w-1/3 lg:w-1/4"
      }`}
    >
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -right-3 top-4 z-10 flex size-6 items-center justify-center rounded-full bg-primary text-white shadow-md"
        aria-label={isCollapsed ? "Expand agent description" : "Collapse agent description"}
      >
        {isCollapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
      </button>

      {!isCollapsed ? (
        <div className="flex h-full flex-col items-center gap-4 overflow-y-auto p-6">
          <span className="text-3xl">{agent.icon}</span>
          <h3 className="mb-6 text-left text-3xl font-bold">{agent.title}</h3>
          <p className="flex-1 overflow-y-auto text-center">
            {agent.description} <br />
          </p>

          <div className="mt-6 w-full">
            <SavedChats savedChats={savedChats} />
          </div>

          <Button onPress={() => setIsCollapsed(true)} className="font-bold md:hidden" color="primary">
            Chat with agent
          </Button>
        </div>
      ) : (
        <div className="flex h-full flex-col items-center py-6">
          <span className="mb-4 text-2xl">{agent.icon}</span>
          <div className="vertical-text rotate-180" style={{ writingMode: "vertical-rl" }}>
            {agent.title}
          </div>
        </div>
      )}
    </div>
  )
}

export default AgentDescription
