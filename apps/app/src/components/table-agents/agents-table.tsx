"use client"
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import Link from "next/link"
import { Plus, Search } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Prisma } from "@prisma/client"

import { columnsAgents } from "./data"
import { RenderCell } from "./render-cell"

type AgentWithBadge = Prisma.AgentGetPayload<{ include: { badge: true } }>

type PaginationInfo = {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
}

interface AgentsTableProps {
  initialAgents: AgentWithBadge[]
  initialPagination: PaginationInfo
}

export const AgentsTable = ({ initialAgents, initialPagination }: AgentsTableProps) => {
  const [agents, setAgents] = useState<AgentWithBadge[]>(initialAgents)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [totalPages, setTotalPages] = useState(initialPagination.totalPages)
  const [filterValue, setFilterValue] = useState("")
  const [filterColumn, setFilterColumn] = useState("name")
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)

  const agentsQuery = trpc.agent.getAllForAdmin.useQuery({ page, pageSize, withBadge: true }, { enabled: false })

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await agentsQuery.refetch()
      if (result.data) {
        setAgents(result.data.data)
        setTotalPages(result.data.pagination.totalPages)
      }
    } finally {
      setIsLoading(false)
    }
  }, [agentsQuery])

  const handleAgentDeleted = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  // Filtrage des agents
  const filteredAgents = React.useMemo(() => {
    if (!filterValue) return agents

    return agents.filter((agent) => {
      switch (filterColumn) {
        case "name":
          return agent.title.toLowerCase().includes(filterValue.toLowerCase())
        case "description":
          return agent.description.toLowerCase().includes(filterValue.toLowerCase())
        case "badge":
          return agent.badge?.title.toLowerCase().includes(filterValue.toLowerCase())
        default:
          return true
      }
    })
  }, [agents, filterValue, filterColumn])

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex w-full flex-wrap justify-between gap-4">
        {/* Filtres */}
        <div className="mb-4 flex gap-4">
          <Input
            isClearable
            className="w-full sm:min-w-[200px] sm:max-w-[44%]"
            placeholder="Rechercher..."
            startContent={<Search className="text-default-300" size={18} />}
            value={filterValue}
            onClear={() => setFilterValue("")}
            onValueChange={setFilterValue}
          />
          <Select
            className="w-full sm:min-w-[150px] sm:max-w-[200px]"
            selectedKeys={[filterColumn]}
            onChange={(e) => setFilterColumn(e.target.value)}
            aria-label="Filtrer par"
          >
            <SelectItem key="name" value="name">
              Nom
            </SelectItem>
            <SelectItem key="description" value="description">
              Description
            </SelectItem>
            <SelectItem key="badge" value="badge">
              Badge
            </SelectItem>
          </Select>
        </div>

        <Link href={`/admin/agents/create`}>
          <Button color="primary" variant="flat" startContent={<Plus size={18} />}>
            Créer un Assistant
          </Button>
        </Link>
      </div>

      {/* Table */}
      <Table
        aria-label="Tableau des agents GPT"
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          {columnsAgents.map((column) => (
            <TableColumn
              key={column.uid}
              hideHeader={column.uid === "actions"}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody
          items={filteredAgents}
          loadingContent={<Spinner label="Chargement..." />}
          loadingState={isLoading ? "loading" : "idle"}
          emptyContent="Aucun agent trouvé"
        >
          {(agent) => (
            <TableRow key={agent.id}>
              {(columnKey) => (
                <TableCell>
                  {RenderCell({
                    agent: agent,
                    columnKey: columnKey.toString(),
                    onAgentDeleted: handleAgentDeleted,
                  })}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
