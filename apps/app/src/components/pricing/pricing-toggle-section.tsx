"use client"

import React, { useState } from "react"
import { useSession } from "next-auth/react"

import PricingToggle from "@/components/pricing/pricing-toggle"
import { Plan } from "@/components/pricing-card"
import PricingCard from "@/components/pricing-card"

interface PricingToggleSectionProps {
  plans: Plan[]
}

export default function PricingToggleSection({ plans }: PricingToggleSectionProps) {
  const session = useSession()
  const lastUserSub = session.data?.user?.lastSubscription
  const [mainPeriod, setMainPeriod] = useState<"MONTHLY" | "ANNUAL">(lastUserSub?.billingPeriod || "MONTHLY")

  const handlePeriodChange = (newPeriod: "MONTHLY" | "ANNUAL") => {
    setMainPeriod(newPeriod)
  }

  return (
    <div className="w-full">
      <PricingToggle onPeriodChange={handlePeriodChange} currentPeriod={mainPeriod} />

      <div className="flex w-full max-w-[1200px] flex-row flex-wrap justify-center gap-4 place-self-center lg:gap-20">
        {plans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={{
              ...plan,
              mainPeriod: mainPeriod,
            }}
          />
        ))}
      </div>
    </div>
  )
}
