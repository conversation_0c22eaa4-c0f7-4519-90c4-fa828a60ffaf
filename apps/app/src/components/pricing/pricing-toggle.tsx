"use client"

import React from "react"

import { Switch } from "@nextui-org/switch"

interface PricingToggleProps {
  onPeriodChange: (period: "MONTHLY" | "ANNUAL") => void
  currentPeriod: "MONTHLY" | "ANNUAL"
}

export default function PricingToggle({ onPeriodChange, currentPeriod }: PricingToggleProps) {
  const isAnnual = currentPeriod === "ANNUAL"

  const handleToggle = (checked: boolean) => {
    onPeriodChange(checked ? "ANNUAL" : "MONTHLY")
  }

  return (
    <div className="mb-8 flex items-center justify-center gap-3 py-4">
      <span className={`text-sm font-medium ${!isAnnual ? "text-primary-600" : "text-default-500"}`}>Mensuel</span>
      <Switch
        size="sm"
        color="primary"
        isSelected={isAnnual}
        onValueChange={handleToggle}
        aria-label="Changer la période de facturation"
      />
      <span className={`text-sm font-medium ${isAnnual ? "text-primary-600" : "text-default-500"}`}>Annuel</span>
    </div>
  )
}
