"use client"

import React, { useState } from "react"

import PricingToggle from "@/components/pricing/pricing-toggle"
import PricingCard, { Plan } from "@/components/pricing-card"

interface PricingSectionProps {
  initialPlans: Plan[]
}

export default function PricingSection({ initialPlans }: PricingSectionProps) {
  const [period, setPeriod] = useState<"MONTHLY" | "ANNUAL">("MONTHLY")
  const [plans, setPlans] = useState<Plan[]>(initialPlans)

  const handlePeriodChange = (newPeriod: "MONTHLY" | "ANNUAL") => {
    setPeriod(newPeriod)

    // Mettre à jour les plans pour refléter la période sélectionnée
    const updatedPlans = initialPlans.map((plan) => {
      if (newPeriod === "ANNUAL") {
        return {
          ...plan,
          price: plan.annualPrice || plan.price,
          period: "ANNUAL",
          originalPrice: plan.isRecommended
            ? Math.round((plan.annualPrice || plan.price) * 1.16 * 100) / 100
            : undefined,
        }
      } else {
        return {
          ...plan,
          price: plan.price,
          period: "MONTHLY",
          originalPrice: plan.isRecommended ? Math.round(plan.price * 1.16 * 100) / 100 : undefined,
        }
      }
    })

    setPlans(updatedPlans)
  }

  return (
    <div className="w-full">
      <PricingToggle currentPeriod={period} onPeriodChange={handlePeriodChange} />

      <div className="flex w-full max-w-[1200px] flex-row flex-wrap justify-center gap-4 place-self-center lg:gap-20">
        {plans.map((plan) => (
          <PricingCard key={plan.id} plan={plan} />
        ))}
      </div>
    </div>
  )
}
