"use client"

import React, { useEffect, useState } from "react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Mo<PERSON><PERSON>eader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { BillingPeriod, Prisma, SubscriptionStatus } from "@prisma/client"

type SubscriptionWithUserAndPlan = Prisma.SubscriptionGetPayload<{
  include: {
    user: {
      select: {
        id: true
        name: true
        email: true
      }
    }
    plan: true
  }
}>

interface UpdateSubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  subscription: SubscriptionWithUserAndPlan
  onSuccess: () => void
}

const UpdateSubscriptionModal = ({ isOpen, onClose, subscription, onSuccess }: UpdateSubscriptionModalProps) => {
  const [selectedPlanId, setSelectedPlanId] = useState<number>(subscription.planId)
  const [status, setStatus] = useState<SubscriptionStatus>(subscription.status)
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>(subscription.billingPeriod)
  const [startDate, setStartDate] = useState<string>(subscription.startDate.toISOString().split("T")[0])
  const [endDate, setEndDate] = useState<string>(subscription.endDate.toISOString().split("T")[0])

  const { data: plans } = trpc.plan.getAll.useQuery()

  const updateSubscription = trpc.subscription.updateByAdmin.useMutation({
    onSuccess: () => {
      toast.success("Abonnement mis à jour avec succès")
      onSuccess()
      onClose()
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`)
    },
  })

  // Reset form when subscription changes
  useEffect(() => {
    setSelectedPlanId(subscription.planId)
    setStatus(subscription.status)
    setBillingPeriod(subscription.billingPeriod)
    setStartDate(subscription.startDate.toISOString().split("T")[0])
    setEndDate(subscription.endDate.toISOString().split("T")[0])
  }, [subscription])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    updateSubscription.mutate({
      id: subscription.id,
      planId: selectedPlanId,
      status,
      billingPeriod,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
    })
  }

  const getStatusLabel = (status: SubscriptionStatus) => {
    switch (status) {
      case "ACTIVE":
        return "Actif"
      case "CANCELED":
        return "Annulé"
      case "EXPIRED":
        return "Expiré"
      case "PENDING":
        return "En attente"
      case "FAILED":
        return "Échec"
      default:
        return status
    }
  }

  const getBillingPeriodLabel = (period: BillingPeriod) => {
    return period === "MONTHLY" ? "Mensuel" : "Annuel"
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalContent>
        {(onClose) => (
          <form onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              Modifier l&apos;abonnement de {subscription.user.name}
            </ModalHeader>
            <ModalBody>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  {!!plans && (
                    <Select
                      label="Plan"
                      selectedKeys={[selectedPlanId.toString()]}
                      onChange={(e) => setSelectedPlanId(Number(e.target.value))}
                      isRequired
                    >
                      {plans?.map((plan) => (
                        <SelectItem key={plan.id.toString()} value={plan.id.toString()}>
                          {plan.name}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                </div>

                <div>
                  <Select
                    label="Statut"
                    selectedKeys={[status]}
                    onChange={(e) => setStatus(e.target.value as SubscriptionStatus)}
                    isRequired
                  >
                    {Object.values(SubscriptionStatus).map((statusValue) => (
                      <SelectItem key={statusValue} value={statusValue}>
                        {getStatusLabel(statusValue)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <Select
                    label="Période de facturation"
                    selectedKeys={[billingPeriod]}
                    onChange={(e) => setBillingPeriod(e.target.value as BillingPeriod)}
                    isRequired
                  >
                    {Object.values(BillingPeriod).map((period) => (
                      <SelectItem key={period} value={period}>
                        {getBillingPeriodLabel(period)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de début"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    isRequired
                  />
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de fin"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    isRequired
                  />
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Annuler
              </Button>
              <Button color="primary" type="submit" isLoading={updateSubscription.isPending}>
                Mettre à jour
              </Button>
            </ModalFooter>
          </form>
        )}
      </ModalContent>
    </Modal>
  )
}

export default UpdateSubscriptionModal
