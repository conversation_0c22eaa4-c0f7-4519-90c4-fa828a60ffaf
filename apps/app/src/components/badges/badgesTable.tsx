"use client"
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { Search } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Prisma } from "@prisma/client"

import { badgesColumns } from "./badges-columns"
import CreateBadgeModal from "./newBadgeModal"
import { RenderCell } from "./render-cell"

type PaginationInfo = {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
}

interface BadgesTableProps {
  initialBadges: BadgeType[]
  initialPagination: PaginationInfo
}

type BadgeType = Prisma.BadgeGetPayload<{
  include: {
    _count: {
      select: {
        agents: true
      }
    }
  }
}>

export const BadgesTable = ({ initialBadges, initialPagination }: BadgesTableProps) => {
  const [badges, setBadges] = useState<BadgeType[]>(initialBadges)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [totalPages, setTotalPages] = useState(initialPagination.totalPages)
  const [filterValue, setFilterValue] = useState("")
  const [filterColumn, setFilterColumn] = useState("title")
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)

  const badgesQuery = trpc.badge.getAllForAdmin.useQuery({ page, pageSize }, { enabled: false })

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await badgesQuery.refetch()
      if (result.data) {
        setBadges(result.data.data)
        setTotalPages(result.data.pagination.totalPages)
      }
    } finally {
      setIsLoading(false)
    }
  }, [badgesQuery])

  const handleBadgesMutated = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  const filteredBadges = React.useMemo(() => {
    if (!filterValue) return badges

    return badges.filter((badge) => {
      return badge.title?.toLowerCase().includes(filterValue.toLowerCase())
    })
  }, [badges, filterValue])

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex w-full flex-wrap justify-between gap-4">
        {/* Filtres */}
        <div className="mb-4 flex gap-4">
          <Input
            isClearable
            className="w-full sm:min-w-[200px] sm:max-w-[44%]"
            placeholder="Rechercher..."
            startContent={<Search className="text-default-300" size={18} />}
            value={filterValue}
            onClear={() => setFilterValue("")}
            onValueChange={setFilterValue}
          />
          <Select
            className="w-full sm:min-w-[150px] sm:max-w-[200px]"
            selectedKeys={[filterColumn]}
            onChange={(e) => setFilterColumn(e.target.value)}
            aria-label="Filtrer par"
          >
            <SelectItem key="title" value="title">
              Titre
            </SelectItem>
          </Select>
        </div>

        <CreateBadgeModal onBadgeCreated={handleBadgesMutated} />
      </div>

      {/* Table */}
      <Table
        aria-label="Tableau des badges"
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          {badgesColumns.map((column) => (
            <TableColumn
              key={column.uid}
              hideHeader={column.uid === "actions"}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody
          items={filteredBadges}
          loadingContent={<Spinner label="Chargement..." />}
          loadingState={isLoading ? "loading" : "idle"}
          emptyContent="Aucun badge trouvé"
        >
          {(badge) => (
            <TableRow key={badge.id}>
              {(columnKey) => (
                <TableCell>
                  {RenderCell({
                    badge: badge,
                    columnKey: columnKey.toString(),
                    onMutation: handleBadgesMutated,
                  })}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
