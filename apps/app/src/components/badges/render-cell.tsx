"use client"

import React, { useState } from "react"
import { <PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma } from "@prisma/client"

const isRenderable = (child: unknown) => {
  return React.isValidElement(child) || typeof child === "string" || typeof child === "number"
}

type BadgeType = Prisma.BadgeGetPayload<{
  include: {
    _count: {
      select: {
        agents: true
      }
    }
  }
}>

interface Props {
  badge: BadgeType
  columnKey: string | React.Key
  onMutation?: () => void
}

export const RenderCell = ({ badge, columnKey, onMutation }: Props) => {
  //@ts-expect-error Any type detected
  const cellValue = badge[columnKey]
  switch (columnKey) {
    case "agentsCount":
      return badge._count.agents
    case "actions":
      return (
        <div className="flex items-center gap-4">
          <EditBadgeModal badge={badge} onBadgeUpdated={onMutation} />
          <DeleteBadgeModal badge={badge} onBadgeDeleted={onMutation} />
        </div>
      )
    default:
      return isRenderable(cellValue) ? cellValue : "<___>"
  }
}

/****
 ** Modals down here
 */

interface EditBadgeModalProps {
  badge: BadgeType
  onBadgeUpdated?: () => void
}

const EditBadgeModal = ({ badge, onBadgeUpdated }: EditBadgeModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState({
    id: badge.id,
    title: badge.title,
  })

  const badgeUpdate = trpc.badge.update.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge mis à jour avec succès!")
      utils.user.getAll.invalidate()
      if (onBadgeUpdated) {
        onBadgeUpdated()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la mise à jour du badge")
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = () => {
    badgeUpdate.mutate(formData)
  }

  return (
    <>
      <Tooltip content="Modifier" color="secondary">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-secondary"
        >
          <Pencil size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Modifier badge</h2>
            <p className="text-sm text-default-500">Mise à jour des informations du badge</p>
          </ModalHeader>
          <ModalBody>
            <form className="flex">
              <Input
                label="Titre"
                name="title"
                value={formData.title || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le titre"
                size="lg"
                variant="bordered"
                labelPlacement="outside"
                classNames={{
                  label: "text-sm font-medium text-default-700 dark:text-default-500",
                }}
              />
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={badgeUpdate.isPending} onPress={handleSubmit} color="secondary" variant="flat">
              Enregistrer les modifications
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

interface DeleteBadgeModalProps {
  badge: BadgeType
  onBadgeDeleted?: () => void
}

const DeleteBadgeModal = ({ badge, onBadgeDeleted }: DeleteBadgeModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)

  const badgeDelete = trpc.badge.delete.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge supprimé avec succès!")
      utils.badge.getAll.invalidate()
      if (onBadgeDeleted) {
        onBadgeDeleted()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la suppression du badge")
    },
  })

  const handleOpenModal = () => {
    if (badge._count.agents > 0) {
      toast.error("Vous ne pouvez pas supprimer ce badge quand des agents sont associés!")
      return
    }
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleBadgeDelete = () => {
    badgeDelete.mutate(badge.id)
  }

  return (
    <>
      <Tooltip content="Supprimer" color="danger">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-danger"
        >
          <Trash size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold text-danger">Confirmation de suppression</h2>
            <p className="text-sm text-default-500">Cette action ne peut pas être annulée</p>
          </ModalHeader>
          <ModalBody>
            <div className="rounded-lg bg-danger-50 p-4">
              <p className="text-sm text-danger">
                Vous êtes sur le point de supprimer définitivement ce badge. Cette action supprimera toutes les données
                associées à ce badge et ne peut pas être annulée.
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light">
              Annuler
            </Button>
            <Button isLoading={badgeDelete.isPending} onPress={handleBadgeDelete} color="danger" variant="flat">
              Supprimer définitivement
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
