"use client"

import React from "react"

const AgentSkeleton = () => {
  return (
    <div className="flex animate-pulse flex-col rounded-xl border border-gray-200 bg-background p-5 shadow-md">
      <div className="flex items-center gap-2 text-2xl">
        <div className="size-8 rounded-full bg-gray-300" />
        <div className="h-8 w-24 rounded bg-gray-300" />
      </div>
      <div className="mt-4 space-y-2">
        <div className="h-6 w-3/4 rounded bg-gray-300" />
        <div className="h-4 w-full rounded bg-gray-300" />
        <div className="h-4 w-full rounded bg-gray-300" />
      </div>
      <div className="mt-4 h-6 w-1/3 rounded bg-gray-300" />
    </div>
  )
}

export default AgentSkeleton
