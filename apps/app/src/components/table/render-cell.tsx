import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, Trash } from "lucide-react"

import { logger } from "@coheadcoaching/lib"
import { Chip } from "@nextui-org/chip"
import { Tooltip } from "@nextui-org/tooltip"
import { User } from "@nextui-org/user"

import { users } from "./data"

interface Props {
  user: (typeof users)[number]
  columnKey: string | React.Key
}

export const RenderCell = ({ user, columnKey }: Props) => {
  //@ts-expect-error Any type detected
  const cellValue = user[columnKey]
  switch (columnKey) {
    case "name":
      return (
        <User
          avatarProps={{
            src: "https://i.pravatar.cc/150?u=a04258114e29026702d",
          }}
          name={cellValue}
        >
          {user.email}
        </User>
      )
    case "role":
      return (
        <div>
          <div>
            <span>{cellValue}</span>
          </div>
          <div>
            <span>{user.team}</span>
          </div>
        </div>
      )
    case "status":
      return (
        <Chip
          size="sm"
          variant="flat"
          color={cellValue === "active" ? "success" : cellValue === "paused" ? "danger" : "warning"}
        >
          <span className="text-xs capitalize">{cellValue}</span>
        </Chip>
      )

    case "actions":
      return (
        <div className="flex items-center gap-4">
          <div>
            <Tooltip content="Details">
              <button onClick={() => logger.log("View user", user.id)}>
                <Eye size={20} className="text-[#979797]" />
              </button>
            </Tooltip>
          </div>
          <div>
            <Tooltip content="Edit user" color="secondary">
              <button onClick={() => logger.log("Edit user", user.id)}>
                <Pencil size={20} className="text-[#979797]" />
              </button>
            </Tooltip>
          </div>
          <div>
            <Tooltip content="Delete user" color="danger" onClick={() => logger.log("Delete user", user.id)}>
              <button>
                <Trash size={20} className="text-[#FF0080]" />
              </button>
            </Tooltip>
          </div>
        </div>
      )
    default:
      return cellValue
  }
}
