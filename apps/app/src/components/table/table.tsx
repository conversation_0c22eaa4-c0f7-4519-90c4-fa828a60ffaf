"use client"
import React from "react"

import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"

import { columns, users } from "./data"
import { RenderCell } from "./render-cell"

export const TableWrapper = () => {
  return (
    <div className="flex w-full flex-col gap-4">
      <Table aria-label="Example table with custom cells">
        <TableHeader>
          {columns.map((column) => (
            <TableColumn
              key={column.uid}
              hideHeader={column.uid === "actions"}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody>
          {users.map((item) => (
            <TableRow key={item.id}>
              {columns.map((column) => (
                <TableCell key={column.uid}>{RenderCell({ user: item, columnKey: column.uid })}</TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
