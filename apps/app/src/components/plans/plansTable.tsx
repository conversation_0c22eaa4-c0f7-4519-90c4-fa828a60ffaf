"use client"

import React, { useCallback, useEffect, useState } from "react"
import { Edit, Trash } from "lucide-react"
import { toast } from "react-toastify"

import { PlanWithParsedFeatures } from "@/api/plan/_router"
import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Chip } from "@nextui-org/chip"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"

import NewPlanModal from "./newPlanModal"
import UpdatePlanModal from "./updatePlanModal"

interface PlansTableProps {
  initialPlans: PlanWithParsedFeatures[]
  initialPagination: {
    page: number
    pageSize: number
    totalCount: number
    totalPages: number
  }
}

export const PlansTable = ({ initialPlans, initialPagination }: PlansTableProps) => {
  const [plans, setPlans] = useState<PlanWithParsedFeatures[]>(initialPlans)
  const [pagination, setPagination] = useState(initialPagination)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [selectedPlan, setSelectedPlan] = useState<PlanWithParsedFeatures | null>(null)
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)

  const plansQuery = trpc.plan.getAllForAdmin.useQuery(
    { page, pageSize },
    { enabled: false, initialData: { data: initialPlans, pagination: initialPagination } }
  )

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await plansQuery.refetch()
      if (result.data) {
        setPlans(result.data.data)
        setPagination(result.data.pagination)
      }
    } finally {
      setIsLoading(false)
    }
  }, [plansQuery])

  const handlePlansMutated = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  const deletePlan = trpc.plan.delete.useMutation({
    onSuccess: () => {
      toast.success("Plan supprimé avec succès")
      handlePlansMutated()
      setIsDeleteModalOpen(false)
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`)
    },
  })

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  const handleEditClick = (plan: PlanWithParsedFeatures) => {
    setSelectedPlan(plan)
    setIsUpdateModalOpen(true)
  }

  const handleDeleteClick = (plan: PlanWithParsedFeatures) => {
    setSelectedPlan(plan)
    setIsDeleteModalOpen(true)
  }

  const confirmDelete = () => {
    if (selectedPlan) {
      deletePlan.mutate(selectedPlan.id)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h3 className="text-xl font-semibold">Liste des plans ({pagination.totalCount})</h3>
        <NewPlanModal onSuccess={handlePlansMutated} />
      </div>

      <Table
        aria-label="Table des plans"
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={pagination.totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          <TableColumn>NOM</TableColumn>
          <TableColumn>PRIX MENSUEL</TableColumn>
          <TableColumn>PRIX ANNUEL</TableColumn>
          <TableColumn>REMB. MENSUEL</TableColumn>
          <TableColumn>REMB. ANNUEL</TableColumn>
          <TableColumn>RECOMMANDÉ</TableColumn>
          <TableColumn>STATUT</TableColumn>
          <TableColumn>ABONNEMENTS</TableColumn>
          <TableColumn>ACTIONS</TableColumn>
        </TableHeader>
        <TableBody
          items={plans}
          loadingState={isLoading ? "loading" : "idle"}
          loadingContent={<Spinner label="Chargement..." />}
          emptyContent="Aucun plan trouvé"
        >
          {(plan) => (
            <TableRow key={plan.id}>
              <TableCell>
                <div>
                  <p className="font-medium">{plan.name}</p>
                  <p className="text-xs text-gray-500">{plan.description || "Aucune description"}</p>
                </div>
              </TableCell>
              <TableCell>{formatPrice(plan.monthlyPrice)}</TableCell>
              <TableCell>{formatPrice(plan.annualPrice)}</TableCell>
              <TableCell>
                {plan.monthlyRefundPercentage && plan.monthlyRefundPercentage > 0 ? (
                  <span className="text-success font-medium">
                    {plan.monthlyRefundPercentage}%
                  </span>
                ) : (
                  <span className="text-default-400">-</span>
                )}
              </TableCell>
              <TableCell>
                {plan.annualRefundPercentage && plan.annualRefundPercentage > 0 ? (
                  <span className="text-success font-medium">
                    {plan.annualRefundPercentage}%
                  </span>
                ) : (
                  <span className="text-default-400">-</span>
                )}
              </TableCell>
              <TableCell>
                {plan.isRecommended ? (
                  <Chip color="warning" variant="flat" size="sm">
                    Recommandé
                  </Chip>
                ) : (
                  <Chip color="default" variant="flat" size="sm">
                    Standard
                  </Chip>
                )}
              </TableCell>
              <TableCell>
                {plan.isActive ? (
                  <Chip color="success" variant="flat" size="sm">
                    Actif
                  </Chip>
                ) : (
                  <Chip color="danger" variant="flat" size="sm">
                    Inactif
                  </Chip>
                )}
              </TableCell>
              <TableCell>{plan._count.subscriptions}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Tooltip content="Modifier">
                    <Button isIconOnly size="sm" variant="light" onPress={() => handleEditClick(plan)}>
                      <Edit size={16} />
                    </Button>
                  </Tooltip>
                  <Tooltip content="Supprimer">
                    <Button isIconOnly size="sm" variant="light" color="danger" onPress={() => handleDeleteClick(plan)}>
                      <Trash size={16} />
                    </Button>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Modal de mise à jour */}
      {selectedPlan && (
        <UpdatePlanModal
          isOpen={isUpdateModalOpen}
          onClose={() => setIsUpdateModalOpen(false)}
          plan={selectedPlan}
          onSuccess={handlePlansMutated}
        />
      )}

      {/* Modal de confirmation de suppression */}
      <div className={`fixed inset-0 z-50 flex items-center justify-center ${isDeleteModalOpen ? "" : "hidden"}`}>
        <div className="fixed inset-0 bg-black/50" onClick={() => setIsDeleteModalOpen(false)}></div>
        <div className="relative z-10 w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
          <h3 className="mb-4 text-xl font-bold">Confirmer la suppression</h3>
          <p>
            Êtes-vous sûr de vouloir supprimer le plan <span className="font-semibold">{selectedPlan?.name}</span> ?
            {selectedPlan && selectedPlan?._count.subscriptions > 0 && (
              <span className="mt-2 block text-red-500">
                Attention: Ce plan a {selectedPlan?._count.subscriptions} abonnement(s) actif(s).
              </span>
            )}
          </p>
          <div className="mt-6 flex justify-end gap-2">
            <Button variant="flat" onPress={() => setIsDeleteModalOpen(false)}>
              Annuler
            </Button>
            <Button color="danger" onPress={confirmDelete} isLoading={deletePlan.isPending}>
              Supprimer
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
