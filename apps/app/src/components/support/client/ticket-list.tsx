"use client"

import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { MessageSquare } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Skeleton } from "@nextui-org/skeleton"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"

const getStatusColor = (status: string): "warning" | "primary" | "success" => {
  switch (status) {
    case "OPEN":
      return "warning"
    case "IN_PROGRESS":
      return "primary"
    case "COMPLETED":
      return "success"
    default:
      return "warning"
  }
}
const ticketStatus = { OPEN: "Ouvert", IN_PROGRESS: "En cours", COMPLETED: "FERME" }

export default function ClientTicketList() {
  const router = useRouter()
  const { data: tickets, isLoading } = trpc.ticket.getUserTickets.useQuery()

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">Vos tickets</h3>
      </div>

      {isLoading ? (
        <div className="flex flex-col gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-16 w-full rounded-lg" />
          ))}
        </div>
      ) : tickets && tickets.length > 0 ? (
        <Table aria-label="Tickets" selectionMode="single" onRowAction={(key) => router.push(`/support/ticket/${key}`)}>
          <TableHeader>
            <TableColumn>Titre</TableColumn>
            <TableColumn>Statut</TableColumn>
            <TableColumn>Dernière mise à jour</TableColumn>
            <TableColumn>Messages</TableColumn>
          </TableHeader>
          <TableBody items={tickets} emptyContent="Aucun ticket trouvé">
            {(ticket) => (
              <TableRow key={ticket.id}>
                <TableCell>{ticket.title}</TableCell>
                <TableCell>
                  <Chip color={getStatusColor(ticket.status)} variant="flat" size="sm">
                    {ticketStatus[ticket.status]}
                  </Chip>
                </TableCell>
                <TableCell>{format(new Date(ticket.updatedAt), "dd/MM/yyyy HH:mm")}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <MessageSquare size={16} className="mr-1" />
                    {ticket._count?.messages || 0}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      ) : (
        <Card>
          <CardBody className="py-8 text-center">
            <p className="text-default-500">
              Pas de ticket <br /> Créez un nouveau ticket ci-dessous
            </p>
          </CardBody>
        </Card>
      )}
    </div>
  )
}
