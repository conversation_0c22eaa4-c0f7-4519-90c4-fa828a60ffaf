"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "react-toastify"

import FileUpload from "@/components/ui/file-upload"
import { maxUploadSize } from "@/constants"
import { trpc } from "@/lib/trpc/client"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"

export default function CreateTicket() {
  const router = useRouter()
  const [title, setTitle] = useState("")
  const [message, setMessage] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const getPresignedUrlMutation = trpc.upload.presignedUrl.useMutation()
  const createTicketMutation = trpc.ticket.create.useMutation()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!title.trim()) {
      toast.error("Le titre du ticket est obligatoire!")
      return
    }

    if (!message.trim()) {
      toast.error("Le message est obligatoire!")
      return
    }

    setIsSubmitting(true)

    try {
      let attachmentId: string | undefined = undefined

      if (file) {
        if (file.size > maxUploadSize) {
          toast.error("Le fichier est trop volumineux!")
          setIsSubmitting(false)
          return
        }

        // Obtenir l'URL présignée via tRPC
        const { url, fields } = await getPresignedUrlMutation.mutateAsync({
          filename: file.name,
          filetype: file.type,
        })

        // Créer le FormData pour l'upload direct vers S3
        const formData = new FormData()
        Object.entries(fields).forEach(([key, value]) => {
          formData.append(key, value as string)
        })
        formData.append("file", file)

        // Upload direct vers S3
        const uploadResponse = await fetch(url, {
          method: "POST",
          body: formData,
        })

        if (!uploadResponse.ok) {
          const xml = await uploadResponse.text()
          const parser = new DOMParser()
          const xmlDoc = parser.parseFromString(xml, "text/xml")
          const error = xmlDoc.getElementsByTagName("Message")[0]
          if (error && error.textContent === "Your proposed upload exceeds the maximum allowed size") {
            toast.error("Le fichier est trop volumineux!")
          } else {
            toast.error("Échec de l'upload du fichier")
          }
          setIsSubmitting(false)
          return
        }

        // Utiliser la clé du fichier comme attachmentId
        attachmentId = fields.key
      }

      // Créer le ticket avec l'attachmentId (qui est la clé du fichier)
      const ticket = await createTicketMutation.mutateAsync({
        title,
        content: message,
        attachmentId,
      })

      toast.success("Le ticket a été créé avec succès!")
      router.push(`/support/ticket/${ticket.id}`)
    } catch (error) {
      console.error("Error:", error)
      toast.error("Échec de création du ticket")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="mx-auto w-full">
      <CardHeader>
        <h3 className="text-xl font-semibold">Nouveau ticket</h3>
      </CardHeader>
      <CardBody>
        <p className="pb-4 text-sm">
          Veuillez apporter toutes les précisions qui pourraient être utiles à notre équipe dans la résolution de votre
          problème : description exhaustive du souci rencontré, capture d&apos;écran, etc. Nous vous répondons sous 24
          heures maximum (Hors dimanche et jours fériés).
        </p>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <Input
            label="Titre du ticket"
            placeholder="Entrez le titre du ticket"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            isRequired
            isDisabled={isSubmitting}
          />

          <Textarea
            label="Message"
            placeholder="Entrez le message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            minRows={5}
            isRequired
            isDisabled={isSubmitting}
          />

          <div className="mt-2">
            <FileUpload
              dictionary={{
                uploadDescription: "Uploadez un fichier (facultatif)",
                invalidFileType: "Type de fichier invalide",
                cropImage: "Recadrer l'image",
                cancel: "Annuler",
                reset: "Réinitialiser",
                save: "Sauvegarder",
                loading: "Chargement",
              }}
              onFilesChange={(files) => setFile(files[0])}
              maxFiles={1}
              accept={{
                "image/png": [".png"],
                "image/jpeg": [".jpg", ".jpeg"],
                "application/pdf": [".pdf"],
                "application/msword": [".doc"],
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
              }}
              disabled={isSubmitting}
            />
          </div>

          <Button type="submit" color="primary" className="mt-4" isLoading={isSubmitting}>
            Envoyer
          </Button>
        </form>
      </CardBody>
    </Card>
  )
}
