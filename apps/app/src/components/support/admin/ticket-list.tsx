"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { MessageSquare, User } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { TicketStatus } from "@/types/support"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Skeleton } from "@nextui-org/skeleton"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tab, Tabs } from "@nextui-org/tabs"

const tabs = {
  OPEN: "Ouverts",
  IN_PROGRESS: "En cours",
  COMPLETED: "Terminés",
}

export default function AdminTicketList() {
  const router = useRouter()
  const [activeStatus, setActiveStatus] = useState<TicketStatus | undefined>(TicketStatus.OPEN)

  const { data, isLoading } = trpc.ticket.getAllForAdmin.useQuery(activeStatus ? { status: activeStatus } : undefined)

  const tickets = data?.tickets
  const ticketsCount = data?.ticketsCount

  const [persistedTicketsCount, setPersistedTicketsCount] = useState<typeof ticketsCount | undefined>(ticketsCount)

  useEffect(() => {
    if (ticketsCount) {
      setPersistedTicketsCount(ticketsCount)
    }
  }, [ticketsCount])

  const handleStatusChange = (key: React.Key) => {
    if (key === "all") {
      setActiveStatus(undefined)
    } else {
      setActiveStatus(key as TicketStatus)
    }
  }

  const getStatusColor = (status: TicketStatus): "warning" | "primary" | "success" => {
    switch (status) {
      case TicketStatus.OPEN:
        return "warning"
      case TicketStatus.IN_PROGRESS:
        return "primary"
      case TicketStatus.COMPLETED:
        return "success"
    }
  }

  return (
    <div className="flex w-full flex-col gap-4">
      <h3 className="text-xl font-semibold">Gestion des tickets</h3>
      <Tabs
        aria-label="Ticket Status"
        selectedKey={activeStatus || "all"}
        onSelectionChange={handleStatusChange}
        className="w-full"
      >
        <Tab key="all" title="Tout" />
        <Tab
          key={TicketStatus.OPEN}
          title={"Ouverts" + (persistedTicketsCount ? ` (${persistedTicketsCount.opened})` : "")}
        />
        <Tab
          key={TicketStatus.IN_PROGRESS}
          title={"En cours" + (persistedTicketsCount ? ` (${persistedTicketsCount.inProgress})` : "")}
        />
        <Tab
          key={TicketStatus.COMPLETED}
          title={"Terminés" + (persistedTicketsCount ? ` (${persistedTicketsCount.completed})` : "")}
        />
      </Tabs>

      {isLoading ? (
        <div className="flex flex-col gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-16 w-full rounded-lg" />
          ))}
        </div>
      ) : tickets && tickets.length > 0 ? (
        <Table
          aria-label="Tickets"
          selectionMode="single"
          onRowAction={(key) => router.push(`/admin/support/ticket/${key}`)}
        >
          <TableHeader>
            <TableColumn>Liste des tickets</TableColumn>
            <TableColumn>Client</TableColumn>
            <TableColumn>Dernière mise à jour</TableColumn>
            <TableColumn>Messages</TableColumn>
          </TableHeader>
          <TableBody items={tickets} emptyContent="Aucun ticket disponible">
            {(ticket) => (
              <TableRow key={ticket.id}>
                <TableCell>
                  <div className="flex flex-col">
                    <span>{ticket.title}</span>
                    {activeStatus === undefined && (
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStatusColor(ticket.status as TicketStatus)}
                        className="mt-1"
                      >
                        {tabs[ticket.status]}
                      </Chip>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <User size={16} />
                    {ticket.user.name || ticket.user.email}
                  </div>
                </TableCell>
                <TableCell>{format(new Date(ticket.updatedAt), "dd/MM/yyyy HH:mm")}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <MessageSquare size={16} className="mr-1" />
                    {ticket._count?.messages || 0}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      ) : (
        <Card>
          <CardBody className="py-8 text-center">
            <p className="text-default-500"> Aucun ticket disponible</p>
          </CardBody>
        </Card>
      )}
    </div>
  )
}
