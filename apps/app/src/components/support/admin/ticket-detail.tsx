"use client"

import { useState } from "react"

import { CloseTicketFooter, StatusDropdown, TicketDetailBase } from "@/components/support/shared"
import { trpc } from "@/lib/trpc/client"
import { TicketStatus } from "@/types/support"

interface AdminTicketDetailProps {
  ticketId: string
}

export default function AdminTicketDetail({ ticketId }: AdminTicketDetailProps) {
  const [refreshKey, setRefreshKey] = useState(0)
  const { data: ticket } = trpc.ticket.getById.useQuery(ticketId)

  const handleStatusChange = () => {
    setRefreshKey(prev => prev + 1)
  }

  const headerActions = ticket ? (
    <StatusDropdown
      ticketId={ticketId}
      currentStatus={ticket.status as TicketStatus}
      onStatusChange={handleStatusChange}
    />
  ) : null

  const footerActions = ticket ? (
    <CloseTicketFooter
      ticketId={ticketId}
      currentStatus={ticket.status as TicketStatus}
      onStatusChange={handleStatusChange}
    />
  ) : null

  return (
    <TicketDetailBase
      key={refreshKey}
      ticketId={ticketId}
      backUrl="/admin/support"
      backLabel="Retour à la liste"
      headerActions={headerActions}
      footerActions={footerActions}
      canSendMessages={true}
      showCreatedAtInHeader={true}
      showSenderName={true}
      containerClassName="mx-auto flex w-full max-w-4xl flex-col gap-4"
    />
  )
}
