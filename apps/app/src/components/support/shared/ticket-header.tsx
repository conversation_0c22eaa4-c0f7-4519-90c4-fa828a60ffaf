"use client"

import { format } from "date-fns"

import { TicketStatus } from "@/types/support"
import { Chip } from "@nextui-org/chip"

interface TicketHeaderProps {
  title: string
  status: TicketStatus
  createdAt: Date
  showCreatedAt?: boolean
  children?: React.ReactNode
}

const statusLabels = {
  OPEN: "Ouvert",
  IN_PROGRESS: "En cours",
  COMPLETED: "Terminé",
}

const getStatusColor = (status: TicketStatus): "warning" | "primary" | "success" => {
  switch (status) {
    case TicketStatus.OPEN:
      return "warning"
    case TicketStatus.IN_PROGRESS:
      return "primary"
    case TicketStatus.COMPLETED:
      return "success"
  }
}

export default function TicketHeader({
  title,
  status,
  createdAt,
  showCreatedAt = true,
  children
}: TicketHeaderProps) {
  return (
    <div className="flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center w-full">
      <div className="flex-1">
        <h3 className="text-xl font-semibold">{title}</h3>
        {showCreatedAt && (
          <div className="mt-1 text-sm text-default-500">
            Créé le: {format(new Date(createdAt), "dd/MM/yyyy HH:mm")}
          </div>
        )}
      </div>
      <div className="flex items-center gap-2">
        <Chip color={getStatusColor(status)} variant="flat">
          {statusLabels[status]}
        </Chip>
        {children}
      </div>
    </div>
  )
}
