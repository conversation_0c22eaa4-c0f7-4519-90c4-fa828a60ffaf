"use client"

import { Key } from "react"
import { ChevronDown } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { TicketStatus } from "@/types/support"
import { Button } from "@nextui-org/button"
import { CardFooter } from "@nextui-org/card"
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@nextui-org/dropdown"

interface AdminStatusControlsProps {
  ticketId: string
  currentStatus: TicketStatus
  onStatusChange: () => void
}

const statusLabels = {
  OPEN: "Ouvert",
  IN_PROGRESS: "En cours",
  COMPLETED: "Terminé",
}

const getStatusColor = (status: TicketStatus): "warning" | "primary" | "success" => {
  switch (status) {
    case TicketStatus.OPEN:
      return "warning"
    case TicketStatus.IN_PROGRESS:
      return "primary"
    case TicketStatus.COMPLETED:
      return "success"
  }
}

export function StatusDropdown({ ticketId, currentStatus, onStatusChange }: AdminStatusControlsProps) {
  const updateStatusMutation = trpc.ticket.updateStatus.useMutation()

  const handleStatusChange = async (status: TicketStatus) => {
    if (currentStatus === status) return

    try {
      await updateStatusMutation.mutateAsync({
        ticketId,
        status,
      })

      onStatusChange()
      toast.success("Statut mis à jour avec succès")
    } catch (error) {
      toast.error("Erreur inconnue")
    }
  }

  return (
    <Dropdown>
      <DropdownTrigger>
        <Button
          variant="flat"
          color={getStatusColor(currentStatus)}
          endContent={<ChevronDown size={16} />}
          size="sm"
        >
          {statusLabels[currentStatus]}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        aria-label="Status options"
        onAction={(key: Key) => handleStatusChange(key as TicketStatus)}
        disallowEmptySelection
        selectionMode="single"
        selectedKeys={[currentStatus]}
      >
        <DropdownItem key={TicketStatus.OPEN}>Ouvert</DropdownItem>
        <DropdownItem key={TicketStatus.IN_PROGRESS}>En cours</DropdownItem>
        <DropdownItem key={TicketStatus.COMPLETED}>Terminé</DropdownItem>
      </DropdownMenu>
    </Dropdown>
  )
}

export function CloseTicketFooter({ ticketId, currentStatus, onStatusChange }: AdminStatusControlsProps) {
  const updateStatusMutation = trpc.ticket.updateStatus.useMutation()

  const handleCloseTicket = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        ticketId,
        status: TicketStatus.COMPLETED,
      })

      onStatusChange()
      toast.success("Ticket clôturé avec succès")
    } catch (error) {
      toast.error("Erreur lors de la clôture du ticket")
    }
  }

  if (currentStatus === TicketStatus.COMPLETED) {
    return null
  }

  return (
    <CardFooter className="flex justify-end border-t border-default-200">
      <Button
        color="success"
        variant="flat"
        onPress={handleCloseTicket}
        startContent={<ChevronDown size={16} />}
        isLoading={updateStatusMutation.isPending}
      >
        Clôturer ce ticket
      </Button>
    </CardFooter>
  )
}
