"use client"

import { useState } from "react"
import { Paperclip, Send } from "lucide-react"
import { toast } from "react-toastify"

import FileUpload from "@/components/ui/file-upload"
import { maxUploadSize } from "@/constants"
import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Textarea } from "@nextui-org/input"
import { Tooltip } from "@nextui-org/tooltip"

interface MessageFormProps {
  ticketId: string
  onMessageSent: () => void
  disabled?: boolean
}

export default function MessageForm({ ticketId, onMessageSent, disabled = false }: MessageFormProps) {
  const [message, setMessage] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const [showAttachment, setShowAttachment] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const getPresignedUrlMutation = trpc.upload.presignedUrl.useMutation()
  const sendMessageMutation = trpc.message.create.useMutation()

  const handleSendMessage = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!message.trim() && !file) return

    setIsSubmitting(true)

    try {
      let attachmentId: string | undefined = undefined

      if (file) {
        if (file.size > maxUploadSize) {
          toast.error("Le fichier est trop volumineux!")
          setIsSubmitting(false)
          return
        }

        const { url, fields } = await getPresignedUrlMutation.mutateAsync({
          filename: file.name,
          filetype: file.type,
        })

        const formData = new FormData()
        Object.entries(fields).forEach(([key, value]) => {
          formData.append(key, value as string)
        })
        formData.append("file", file)

        const uploadResponse = await fetch(url, {
          method: "POST",
          body: formData,
        })

        if (uploadResponse.ok) {
          // Utiliser la clé du fichier comme attachmentId
          attachmentId = fields.key
        } else {
          throw new Error("Upload failed")
        }
      }

      // Créer le message avec l'attachmentId
      await sendMessageMutation.mutateAsync({
        ticketId,
        content: message.trim() || " ", // Espace si seulement pièce jointe
        attachmentId,
      })

      setMessage("")
      setFile(null)
      setShowAttachment(false)
      onMessageSent()
    } catch (error) {
      console.error("Error sending message:", error)
      toast.error("Échec de l'envoi du message!")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (disabled) {
    return null
  }

  return (
    <form onSubmit={handleSendMessage} className="mt-4">
      {showAttachment && (
        <div className="mb-4">
          <FileUpload
            dictionary={{
              uploadDescription: "Uploadez un fichier",
              invalidFileType: "Type de fichier invalide",
              cropImage: "Recadrer l'image",
              cancel: "Annuler",
              reset: "Réinitialiser",
              save: "Sauvegarder",
              loading: "Chargement",
            }}
            onFilesChange={(files) => setFile(files[0])}
            maxFiles={1}
            accept={{
              "image/png": [".png"],
              "image/jpeg": [".jpg", ".jpeg"],
              // "application/pdf": [".pdf"],
            }}
            disabled={isSubmitting}
          />
        </div>
      )}

      <div className="flex gap-2">
        <Tooltip content="Joindre un fichier">
          <Button isIconOnly variant="flat" onPress={() => setShowAttachment(!showAttachment)}>
            <Paperclip size={20} />
          </Button>
        </Tooltip>

        <Textarea
          placeholder="Écrivez un message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          minRows={1}
          maxRows={4}
          disabled={isSubmitting}
          classNames={{
            input: "resize-none",
            inputWrapper: "border-2 focus-within:border-primary/50",
          }}
        />

        <Button
          isIconOnly
          color="primary"
          type="submit"
          isDisabled={isSubmitting || (!message.trim() && !file)}
          isLoading={isSubmitting}
        >
          <Send size={20} />
        </Button>
      </div>
    </form>
  )
}
