"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { ArrowLeft } from "lucide-react"

import ImageViewer from "@/components/ui/image-viewer"
import { trpc } from "@/lib/trpc/client"
import { TicketStatus } from "@/types/support"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Skeleton } from "@nextui-org/skeleton"

import MessageForm from "./message-form"
import MessageList from "./message-list"
import TicketHeader from "./ticket-header"

interface TicketDetailBaseProps {
  ticketId: string
  backUrl: string
  backLabel?: string
  headerActions?: React.ReactNode
  footerActions?: React.ReactNode
  canSendMessages?: boolean
  showCreatedAtInHeader?: boolean
  showSenderName?: boolean
  containerClassName?: string
}

export default function TicketDetailBase({
  ticketId,
  backUrl,
  backLabel = "Retour à la liste",
  headerActions,
  footerActions,
  canSendMessages = true,
  showCreatedAtInHeader = false,
  showSenderName = false,
  containerClassName = "mx-auto flex w-full flex-col gap-4",
}: TicketDetailBaseProps) {
  const router = useRouter()
  const { data: ticket, isLoading, refetch } = trpc.ticket.getById.useQuery(ticketId, { refetchInterval: 5000 })

  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState<{ url: string; filename: string } | null>(null)

  const openImageViewer = (url: string, filename: string) => {
    setSelectedImage({ url, filename })
    setImageModalOpen(true)
  }

  const handleMessageSent = () => {
    refetch()
  }

  if (isLoading) {
    return (
      <div className={containerClassName}>
        <Button
          variant="light"
          startContent={<ArrowLeft size={20} />}
          onPress={() => router.push(backUrl)}
          className="mb-2 w-fit"
        >
          {backLabel}
        </Button>

        <Card className="w-full">
          <CardHeader className="flex gap-3">
            <Skeleton className="h-8 w-3/4 rounded-lg" />
          </CardHeader>
          <CardBody>
            <Skeleton className="h-32 w-full rounded-lg" />
          </CardBody>
        </Card>
      </div>
    )
  }

  if (!ticket) return null

  const isTicketCompleted = ticket.status === TicketStatus.COMPLETED
  const shouldShowMessageForm = canSendMessages && !isTicketCompleted

  return (
    <div className={containerClassName}>
      <Button
        variant="light"
        as={Link}
        startContent={<ArrowLeft size={20} />}
        href={backUrl}
        className="mb-2 w-fit"
      >
        {backLabel}
      </Button>

      <Card className="w-full">
        <CardHeader>
          <TicketHeader
            title={ticket.title}
            status={ticket.status as TicketStatus}
            createdAt={ticket.createdAt}
            showCreatedAt={showCreatedAtInHeader}
          >
            {headerActions}
          </TicketHeader>
        </CardHeader>

        <Divider />

        <CardBody className="px-4 py-2">
          {!showCreatedAtInHeader && (
            <div className="mb-4 text-sm text-default-500">
              Créé le: {format(new Date(ticket.createdAt), "dd/MM/yyyy HH:mm")}
            </div>
          )}

          <MessageList
            messages={ticket.messages}
            onImageView={openImageViewer}
            showSenderName={showSenderName}
          />

          {shouldShowMessageForm && (
            <MessageForm
              ticketId={ticketId}
              onMessageSent={handleMessageSent}
              disabled={isTicketCompleted}
            />
          )}
        </CardBody>

        {footerActions}
      </Card>

      {/* Modal pour visualiser les images en plein écran */}
      {selectedImage && (
        <ImageViewer
          isOpen={imageModalOpen}
          onClose={() => setImageModalOpen(false)}
          imageUrl={selectedImage.url}
          filename={selectedImage.filename}
        />
      )}
    </div>
  )
}
