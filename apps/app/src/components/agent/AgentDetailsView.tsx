"use client"

import React from "react"
import <PERSON> from "next/link"
import { Arrow<PERSON>ef<PERSON>, Edit, Trash } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Prisma } from "@prisma/client"

// Définition complète du type d'Agent avec toutes les relations nécessaires
type AgentWithDetails = Prisma.AgentGetPayload<{
  include: {
    badge: true
    skills: true
    prompts: true
    _count: {
      select: {
        chats: true
      }
    }
  }
}>

interface AgentDetailsViewProps {
  agent: AgentWithDetails
  onDeleteClick?: (agent: AgentWithDetails) => void
  isAdmin?: boolean
}

const AgentDetailsView: React.FC<AgentDetailsViewProps> = ({ agent, onDeleteClick, isAdmin = false }) => {
  return (
    <div className="container mx-auto max-w-5xl px-4 py-6">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href="/admin/agents">
            <Button isIconOnly variant="light" aria-label="Retour">
              <ArrowLeft />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {agent.icon} {agent.title}
          </h1>
          {agent.badge && (
            <Chip size="sm" variant="flat" color="warning">
              {agent.badge.title}
            </Chip>
          )}
        </div>

        {isAdmin && (
          <div className="flex gap-2">
            <Link href={`/admin/agents/${agent.id}/edit`}>
              <Button color="secondary" startContent={<Edit size={18} />}>
                Modifier
              </Button>
            </Link>
            {onDeleteClick && (
              <Button color="danger" startContent={<Trash size={18} />} onPress={() => onDeleteClick(agent)}>
                Supprimer
              </Button>
            )}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader className="pb-0 pt-4">
            <h2 className="text-lg font-semibold">Informations générales</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="text-md font-medium text-gray-500">Description</h3>
                <p>{agent.description}</p>
              </div>

              <div>
                <h3 className="text-md font-medium text-gray-500">Modèle</h3>
                <p>{agent.model}</p>
              </div>

              <div>
                <h3 className="text-md font-medium text-gray-500">Température</h3>
                <p>{agent.temperature}</p>
              </div>

              <div>
                <h3 className="text-md font-medium text-gray-500">Personnalité</h3>
                <p>{agent.personality || "Non définie"}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="col-span-1">
          <CardHeader className="pb-0 pt-4">
            <h2 className="text-lg font-semibold">Compétences</h2>
          </CardHeader>
          <CardBody>
            <div className="flex flex-wrap gap-2">
              {agent.skills.length > 0 ? (
                agent.skills.map((skill) => (
                  <Chip key={skill.id} variant="flat" color="primary">
                    {skill.name}
                  </Chip>
                ))
              ) : (
                <p className="text-gray-500">Aucune compétence définie</p>
              )}
            </div>
          </CardBody>
        </Card>

        <Card className="col-span-1 lg:col-span-3">
          <CardHeader className="pb-0 pt-4">
            <h2 className="text-lg font-semibold">Prompt système</h2>
          </CardHeader>
          <CardBody>
            {agent.prompts.length > 0 ? (
              <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
                <pre className="whitespace-pre-wrap font-sans text-sm">{agent.prompts[0].body}</pre>
              </div>
            ) : (
              <p className="text-gray-500">Aucun prompt défini</p>
            )}
          </CardBody>
        </Card>

        <Card className="col-span-1 lg:col-span-3">
          <CardHeader className="pb-0 pt-4">
            <h2 className="text-lg font-semibold">Statistiques</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                <h3 className="text-md font-medium text-blue-700 dark:text-blue-300">Total de chats</h3>
                <p className="text-2xl font-bold">{agent._count.chats}</p>
              </div>

              {/* D'autres statistiques peuvent être ajoutées ici */}
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default AgentDetailsView
