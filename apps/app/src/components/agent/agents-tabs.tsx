"use client"

import React from "react"

import AdminChatbox from "@/components/adminChatbox"
import AgentConfigAdmin from "@/components/agentConfigAdmin"
import { Card, CardBody } from "@nextui-org/card"
import { Tab, Tabs } from "@nextui-org/tabs"
import { Prisma } from "@prisma/client"

type AgentWithDetails = Prisma.AgentGetPayload<{
  include: { skills: true; prompts: true; badge: true }
}>

interface AgentTabsProps {
  agent: AgentWithDetails
}

const AgentTabs: React.FC<AgentTabsProps> = ({ agent }) => {
  return (
    <Tabs aria-label="Options" fullWidth>
      <Tab key="config" title="Configuration">
        <Card>
          <CardBody>
            <AgentConfigAdmin agent={agent} />
          </CardBody>
        </Card>
      </Tab>
      <Tab key="chatbox" title="Test de l'agent">
        <Card>
          <CardBody className="p-0">
            <AdminChatbox agent={agent} />
          </CardBody>
        </Card>
      </Tab>
    </Tabs>
  )
}

export default AgentTabs
