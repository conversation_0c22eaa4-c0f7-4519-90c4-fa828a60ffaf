import { FC } from "react"
import { CheckIcon } from "lucide-react"

import { useCheckbox } from "@nextui-org/checkbox"
import { tv } from "@nextui-org/theme"

interface CustomCheckboxProps {
  value?: string
  className?: string
  children?: React.ReactNode
  isSelected?: boolean
  onChange?: () => void
}

export const CustomCheckbox: FC<CustomCheckboxProps> = (props: CustomCheckboxProps) => {
  const checkbox = tv({
    slots: {
      base: "flex items-center gap-2 px-3 py-2 border rounded-md cursor-pointer transition-colors",
      content: "text-default-500",
    },
    variants: {
      isSelected: {
        true: {
          base: "border-primary bg-primary text-primary-foreground hover:bg-primary-500",
          content: "text-white",
        },
      },
      isFocusVisible: {
        true: {
          base: "outline-none ring-2 ring-focus ring-offset-2 ring-offset-background",
        },
      },
    },
  })

  const { children, isSelected, isFocusVisible, getBaseProps, getLabelProps, getInputProps } = useCheckbox({ ...props })

  const styles = checkbox({ isSelected, isFocusVisible })

  return (
    <label {...getBaseProps()} className={styles.base()}>
      {/* Input caché (mais accessible) */}
      <input {...getInputProps()} className="hidden" type="checkbox" />

      {/* Icône Check uniquement si sélectionné */}
      {isSelected && <CheckIcon className="text-white" />}

      {/* Texte du bouton */}
      <span {...getLabelProps()} className={styles.content() + ` ${props.className}`}>
        {children || (isSelected ? "Enabled" : "Disabled")}
      </span>
    </label>
  )
}
