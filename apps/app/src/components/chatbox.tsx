"use client"

import React, { useEffect, useRef, useState } from "react"
import { Message } from "ai"
import { AnimatePresence, motion } from "framer-motion"
import {
  Al<PERSON><PERSON>riangle,
  BookmarkCheck,
  BookmarkPlus,
  BookmarkX,
  MessageCircleWarning,
  Square,
  SquarePen,
} from "lucide-react"
import { toast } from "react-toastify"

import { useChatContext } from "@/contexts/ChatContext"
import { useChat } from "@ai-sdk/react"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Progress } from "@nextui-org/progress"
import { Tooltip } from "@nextui-org/tooltip"
import { Agent } from "@prisma/client"

import { Icons } from "./icons"

interface ChatboxProps {
  agent: Agent
}

const Chatbox: React.FC<ChatboxProps> = ({ agent }) => {
  const {
    chatId,
    isSaved,
    saveCurrentChat,
    resetChat,
    setIsSaved,
    agentLimit,
    hasConversationWithCurrentAgent,
    agentLimitReached,
    messageLimit,
    saveLimit,
    canSave,
  } = useChatContext()
  const [hasReachedMessageLimit, setHasReachedMessageLimit] = useState(false)

  // Track which chat we're currently displaying
  const [loadedChatId, setLoadedChatId] = useState<string | null>(null)

  // Flag to indicate a truly NEW chat (never populated with messages)
  const [isGenuinelyNewChat, setIsGenuinelyNewChat] = useState(false)

  // Keep track of chats we've seen before to prevent re-initiating
  const seenChatsRef = useRef<Set<string>>(new Set())
  // Skip fetch flag to prevent loading after saving
  const skipFetchRef = useRef(false)

  const { messages, input, handleInputChange, handleSubmit, status, stop, setMessages } = useChat({
    body: {
      agentId: agent.id,
      filestoChatWith: [5, 4, 6],
      chatId: chatId || undefined,
    },
    onError: (error) => {
      logger.error(error)
      toast.error(error.message)
    },
  })

  // Vérifier si on a atteint la limite de messages
  useEffect(() => {
    if (messageLimit !== null) {
      setHasReachedMessageLimit(messages.length >= messageLimit)
    }
  }, [messages.length, messageLimit])

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const formRef = useRef<HTMLFormElement>(null)
  const [userHasScrolled, setUserHasScrolled] = useState(false)

  // Handle scroll detection
  const handleScroll = (): void => {
    if (!messagesContainerRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50

    setUserHasScrolled(!isAtBottom)
  }

  // Determine if a chat is new when chatId changes
  useEffect(() => {
    // If we have a chatId, it's an existing chat
    if (chatId) {
      setIsGenuinelyNewChat(false)
      if (!seenChatsRef.current.has(chatId)) {
        seenChatsRef.current.add(chatId)
      }
    } else {
      // No chatId means potentially new chat
      // Check if we've loaded a chat previously
      setIsGenuinelyNewChat(!loadedChatId)
    }
  }, [chatId, loadedChatId])

  // Load chat messages when chatId changes
  useEffect(() => {
    async function loadChatFromApi(id: string): Promise<void> {
      try {
        if (skipFetchRef.current) {
          skipFetchRef.current = false
          setLoadedChatId(id)
          return
        }

        const response = await fetch(`/api/chat/${id}`)

        if (!response.ok) {
          toast.error("Erreur lors du chargement du chat")
          resetChat()
          logger.error("Failed to load chat:", await response.text())
          return
        }

        const data = (await response.json()) as { messages: Message[] }
        if (data.messages) {
          setMessages(data.messages)
          setLoadedChatId(id)
          seenChatsRef.current.add(id)
          setIsSaved(true)
        }
      } catch (error) {
        logger.error("Error loading chat messages:", error)
      }
    }

    if (chatId && chatId !== loadedChatId) {
      loadChatFromApi(chatId)
    } else if (!chatId && loadedChatId) {
      setMessages([])
      setLoadedChatId(null)
    }
  }, [chatId, loadedChatId, setMessages, setIsSaved, resetChat])

  // Auto-scroll to bottom
  useEffect(() => {
    if (!userHasScrolled && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages, userHasScrolled])

  // Simple way to detect waiting for AI's first response
  const isWaitingForAIResponse =
    messages.length === 1 && // Has only empty user message
    messages[0].role === "user" && // First message is from user
    messages[0].content === "" && // First message is empty
    status !== "streaming" // Not currently streaming

  const handleSaveCurrentChat = async () => {
    try {
      await saveCurrentChat(messages)
      skipFetchRef.current = true
      setLoadedChatId(chatId)
      // Mettre à jour l'état après sauvegarde
      // setCanSave(false)
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message || "Erreur lors de la sauvegarde du chat")
      } else {
        toast.error("Erreur lors de la sauvegarde du chat")
      }
      logger.error("Error saving chat:", error)
    }
  }

  const handleResetChat = () => {
    resetChat()
    setMessages([])
  }

  const handleStartChat = () => {
    handleSubmit(new Event("submit") as Event, {
      allowEmptySubmit: true,
    })
  }

  const showStartButton = isGenuinelyNewChat && !chatId && messages.length === 0

  // Calculer le pourcentage d'utilisation des messages
  const messageUsagePercent = messageLimit ? Math.min(100, Math.round((messages.length / messageLimit) * 100)) : 0
  const isNearMessageLimit = messageLimit && messages.length >= messageLimit * 0.8

  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center justify-between border-b p-4">
        <h2 className="text-xl font-bold">Chat with {agent.title}</h2>

        <div className="flex items-center gap-2">
          {messages.length > 0 && !isSaved && (
            <Tooltip
              content={!canSave ? `Limite de ${saveLimit} chats sauvegardés atteinte` : "Sauvegarder la conversation"}
            >
              <Button
                isIconOnly
                variant="light"
                onPress={handleSaveCurrentChat}
                aria-label="Sauvegarder la conversation"
                isDisabled={!canSave}
              >
                {!canSave ? <BookmarkX className="size-5 text-warning" /> : <BookmarkPlus className="size-5" />}
              </Button>
            </Tooltip>
          )}

          {isSaved && (
            <div className="text-sm text-green-600 dark:text-green-400">
              <Tooltip content="Conversation sauvegardée">
                <span className="inline-flex size-10 items-center justify-center" aria-label="Conversation sauvegardée">
                  <BookmarkCheck className="size-5" />
                </span>
              </Tooltip>
            </div>
          )}
          <Tooltip content="Nouvelle conversation">
            <Button isIconOnly variant="light" onPress={handleResetChat} aria-label="Nouvelle conversation">
              <SquarePen className="size-5" />
            </Button>
          </Tooltip>
        </div>
      </div>

      {/* Indicateur de limite de messages */}
      {messageLimit && isNearMessageLimit && (
        <div className="border-b px-4 py-2">
          <div className="flex items-center gap-2 text-sm">
            <MessageCircleWarning className={`size-4 ${hasReachedMessageLimit ? "text-red-500" : "text-yellow-500"}`} />
            <span>
              {hasReachedMessageLimit
                ? `Limite de ${messageLimit} messages atteinte`
                : `${messages.length}/${messageLimit} messages`}
            </span>
          </div>
          <Progress
            size="sm"
            value={messageUsagePercent}
            color={hasReachedMessageLimit ? "danger" : messageUsagePercent > 90 ? "warning" : "primary"}
            className="mt-1"
          />
        </div>
      )}

      {/* Chat Messages */}
      <div
        ref={messagesContainerRef}
        className={`grow space-y-4 p-4 ${showStartButton ? "overflow-y-hidden" : "overflow-y-auto"}`}
        onScroll={handleScroll}
      >
        {showStartButton ? (
          <div className="flex h-full flex-col items-center justify-center">
            <div className="mb-4 text-center">
              <h3 className="text-lg font-medium">Prêt à discuter avec {agent.title}?</h3>
              {agentLimitReached && !hasConversationWithCurrentAgent ? (
                <div className="mt-2 flex items-center justify-center gap-2 text-red-500">
                  <AlertTriangle className="size-4" />
                  <p className="text-sm">Vous avez atteint la limite de {agentLimit} agents pour votre plan actuel.</p>
                </div>
              ) : (
                <p className="text-sm text-gray-500">Cliquez sur le bouton pour commencer la conversation</p>
              )}
            </div>
            <Button
              color="primary"
              size="lg"
              onPress={handleStartChat}
              className="px-8"
              isDisabled={agentLimitReached && !hasConversationWithCurrentAgent}
            >
              Commencer
            </Button>
          </div>
        ) : (
          <>
            <AnimatePresence initial={false}>
              {messages.map((msg, index) => (
                <motion.div
                  key={msg.id || index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  {msg.content !== "" && (
                    <div
                      className={`max-w-[85%] whitespace-pre-wrap rounded-lg p-3 ${msg.role === "user" ? "bg-primary-300 text-white" : "bg-gray-100 dark:bg-gray-800"
                        }`}
                    >
                      {msg.content}
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>

            {isWaitingForAIResponse && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="flex justify-start"
              >
                <div className="max-w-[85%] rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <Icons.typing />
                </div>
              </motion.div>
            )}

            {!isWaitingForAIResponse && (status === "submitted" || status === "streaming") && (
              <div className="flex w-full justify-center gap-2">
                <Button type="button" color="primary" className="animate-pulse" onPress={() => stop()}>
                  <Square className="fill-white" />
                  Stop
                </Button>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="border-t p-4">
        <form ref={formRef} className="flex gap-2" onSubmit={handleSubmit}>
          <Input
            value={input}
            onChange={handleInputChange}
            onBlur={(e) => e.target.focus()}
            autoFocus={true}
            autoComplete="off"
            placeholder={hasReachedMessageLimit ? "Limite de messages atteinte" : "Envoyez un message..."}
            fullWidth
            disabled={status === "streaming" || showStartButton || hasReachedMessageLimit}
            classNames={{
              input: "min-h-12",
            }}
          />
          <Button
            type="submit"
            color="primary"
            isLoading={status === "streaming"}
            className="min-w-[80px]"
            isDisabled={showStartButton || hasReachedMessageLimit}
          >
            {status === "streaming" ? "" : "Envoyer"}
          </Button>
        </form>
      </div>
    </div>
  )
}

export default Chatbox
