import Link from "next/link"
import { DollarSign, Headset, Shield } from "lucide-react"

import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { dictionaryRequirements } from "@/lib/utils/dictionary"
import { hasRole } from "@/lib/utils/user-utils"
import { Avatar } from "@nextui-org/avatar"
import { Button } from "@nextui-org/button"
import { Tooltip } from "@nextui-org/tooltip"
import { UserRole } from "@prisma/client"

import SignoutButton from "./auth/sign-out-button"
import { ThemeSwitch } from "./theme/theme-switch"
import { Icons } from "./icons"
import LocaleSwitcher from "./locale-switcher"
import MobileMenu from "./mobile-menu"

export default async function NavSettings({ lang }: { lang: Locale }) {
  const dictionary = await getDictionary(
    lang,
    dictionaryRequirements({
      profile: true,
      profilePage: {
        serverSideData: true,
      },
      deleteAccount: true,
      signOut: true,
      signIn: true,
    })
  )
  const session = await auth()
  const user = session?.user

  return (
    <div className="sticky left-0 top-0 z-20 w-full bg-background px-6">
      <div className="mx-auto flex w-full max-w-[1200px] items-center justify-between border-b border-gray-200 py-4">
        {/* Logo */}
        <Link href="/">
          <span className="sr-only">Cohead Logo</span>
          <Icons.logo className="size-14 text-foreground" />
        </Link>

        {/* Desktop Menu */}
        <div className="hidden items-center gap-4 md:flex">
          <ThemeSwitch />
          <LocaleSwitcher lang={lang} />
          {hasRole(user?.roles, [UserRole.ADMIN, UserRole.IA_BUILDER, UserRole.MODO, UserRole.SAV]) ? (
            <Link href={"/admin"}>
              <Tooltip content="Accéder à l'espace admin">
                <Shield className="size-7 text-default-500" />
              </Tooltip>
            </Link>
          ) : (
            <Link href={"/support"}>
              <Tooltip content="Accéder au support client">
                <Headset className="size-7 text-default-500" />
              </Tooltip>
            </Link>
          )}
          <Link href={"/pricing"}>
            <Tooltip content="Voir les tarifications">
              <DollarSign className="size-7 text-default-500" />
            </Tooltip>
          </Link>
          {user ? (
            <>
              <Link className="size-fit" href="/profile">
                <Avatar src={user.image} name={user.username} />
              </Link>
              <SignoutButton>{dictionary.signOut}</SignoutButton>
            </>
          ) : (
            <Link href="/sign-in">
              <Button variant="ghost" type="button">
                {dictionary.signIn}
              </Button>
            </Link>
          )}
        </div>

        {/* Mobile Menu */}
        <MobileMenu lang={lang} user={user} dictionary={dictionary} />
      </div>
    </div>
  )
}
