import { auth } from "@/lib/auth"

import { SidebarWrapperClient } from "./sidebar-client"

/**
 * Composant serveur qui récupère les données utilisateur et les passe à la sidebar client
 */
export async function ServerSidebarProvider() {
  // Récupérer la session utilisateur
  const session = await auth()

  if (!session?.user?.id) {
    return null
  }

  const user = session?.user

  if (!user) {
    return null
  }

  // Passer les données utilisateur au composant client
  return <SidebarWrapperClient user={user} />
}
