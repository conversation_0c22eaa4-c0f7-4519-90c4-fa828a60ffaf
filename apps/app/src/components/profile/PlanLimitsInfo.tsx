"use client"

import React, { useCallback, useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Info, MessageSquare, Tag } from "lucide-react"

import { canSaveMoreChats, getAllUserSavedChats } from "@/actions/chat-actions"
import { canUserSelectMoreCategories, getUserPlanRestrictions, hasReachedAgentLimit } from "@/lib/plan"
import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { Card, CardBody, CardFooter, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Progress } from "@nextui-org/progress"
import { Tooltip } from "@nextui-org/tooltip"

import CategorySelectionButton from "./CategorySelectionButton"

interface PlanLimitsInfoProps {
  userId?: string
}

const PlanLimitsInfo: React.FC<PlanLimitsInfoProps> = ({ userId }) => {
  const { data: session } = useSession()
  const [restrictions, setRestrictions] = useState<Array<{ type: string; value: number | null }>>([])
  const [usageData, setUsageData] = useState<{
    savedChats: number
    maxSavedChats: number | null
  }>({
    savedChats: 0,
    maxSavedChats: null,
  })
  const [agentUsageData, setAgentUsageData] = useState<{
    usedAgents: number
    maxAgents: number | null
  }>({
    usedAgents: 0,
    maxAgents: null,
  })
  const [categoryUsageData, setCategoryUsageData] = useState<{
    usedCategories: number
    maxCategories: number | null
    canModify: boolean
    remainingDays: number
  }>({
    usedCategories: 0,
    maxCategories: null,
    canModify: false,
    remainingDays: 0,
  })
  const [loading, setLoading] = useState(true)

  const effectiveUserId = userId || session?.user?.id

  // Récupérer les sélections de catégories de l'utilisateur
  const { data: userSelections } = trpc.badge.getUserCategorySelections.useQuery(undefined, {
    enabled: !!effectiveUserId,
    staleTime: 30000, // 30 secondes
    gcTime: 60000, // 1 minute
  })

  // Récupérer les catégories disponibles
  const { data: categories } = trpc.badge.getActiveWithAgents.useQuery(undefined, {
    staleTime: 60000, // 1 minute
    gcTime: 300000, // 5 minutes
  })

  // État pour stocker les conversations sauvegardées
  const [savedChats, setSavedChats] = useState<Array<{ id: string; title: string | null; agentName?: string }>>([])

  // Fonction pour récupérer toutes les conversations sauvegardées de l'utilisateur
  const fetchSavedChats = useCallback(async () => {
    if (!effectiveUserId) return

    try {
      const chats = await getAllUserSavedChats(5) // Limiter à 5 conversations pour l'affichage
      setSavedChats(chats)
    } catch (error) {
      console.error("Error fetching saved chats:", error)
    }
  }, [effectiveUserId])

  useEffect(() => {
    const fetchRestrictions = async () => {
      if (!effectiveUserId) return

      try {
        const userRestrictions = await getUserPlanRestrictions(effectiveUserId)
        logger.log("restrictions", userRestrictions)
        setRestrictions(userRestrictions)

        // Récupérer les données d'utilisation des chats
        const { limit, savedChatsCount } = await canSaveMoreChats()

        setUsageData({
          savedChats: savedChatsCount,
          maxSavedChats: limit,
        })

        // Récupérer les données d'utilisation des agents
        const { limit: agentLimit, uniqueAgentsCount } = await hasReachedAgentLimit(effectiveUserId)

        setAgentUsageData({
          usedAgents: uniqueAgentsCount,
          maxAgents: agentLimit,
        })

        // Récupérer les données d'utilisation des catégories
        const {
          limit: categoryLimit,
          currentCount,
          canSelect,
          remainingDays,
        } = await canUserSelectMoreCategories(effectiveUserId)

        setCategoryUsageData({
          usedCategories: currentCount,
          maxCategories: categoryLimit,
          canModify: canSelect,
          remainingDays,
        })

        // Récupérer les conversations sauvegardées
        await fetchSavedChats()
      } catch (error) {
        console.error("Error fetching plan restrictions:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchRestrictions()
  }, [effectiveUserId, fetchSavedChats])

  if (loading || !effectiveUserId) {
    return null
  }

  const formatRestrictionLabel = (type: string, value: number | null): string => {
    switch (type) {
      case "MAX_MESSAGES_PER_CHAT":
        return `${value === null ? "Illimité" : value} messages par conversation`
      case "MAX_SAVED_CHATS":
        return `${value === null ? "Illimité" : value} conversations sauvegardées`
      case "MAX_AGENTS":
        return `${value === null ? "Illimité" : value} agents disponibles`
      case "MAX_CATEGORIES":
        return `${value === null ? "Illimité" : value} catégories sélectionnables`
      default:
        return `${type}: ${value === null ? "Illimité" : value}`
    }
  }

  // Calculer le pourcentage d'utilisation des chats sauvegardés
  const savedChatsPercent = usageData.maxSavedChats
    ? Math.min(100, Math.round((usageData.savedChats / usageData.maxSavedChats) * 100))
    : 0

  // Calculer le pourcentage d'utilisation des agents
  const agentUsagePercent = agentUsageData.maxAgents
    ? Math.min(100, Math.round((agentUsageData.usedAgents / agentUsageData.maxAgents) * 100))
    : 0

  // Calculer le pourcentage d'utilisation des catégories
  const categoryUsagePercent = categoryUsageData.maxCategories
    ? Math.min(100, Math.round((categoryUsageData.usedCategories / categoryUsageData.maxCategories) * 100))
    : 0

  // Récupérer les noms des catégories sélectionnées
  const selectedCategoryNames =
    userSelections?.categoryIds && categories
      ? categories
          .filter((category) => userSelections.categoryIds.includes(category.id))
          .map((category) => category.title)
      : []

  return (
    <Card className="w-full">
      <CardHeader className="flex justify-between">
        <h3 className="text-lg font-bold">Limites de votre plan</h3>
        <Tooltip
          content={
            <div className="p-2">
              <ul className="space-y-1">
                {restrictions.map((restriction) => (
                  <li key={restriction.type}>{formatRestrictionLabel(restriction.type, restriction.value)}</li>
                ))}
              </ul>
            </div>
          }
        >
          <div className="cursor-pointer">
            <Info size={16} />
          </div>
        </Tooltip>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          {/* Conversations sauvegardées */}
          {usageData.maxSavedChats && (
            <div>
              <div className="mb-1 flex justify-between">
                <span>Conversations sauvegardées</span>
                <span>
                  {usageData.savedChats}/{usageData.maxSavedChats}
                </span>
              </div>
              <Progress
                value={savedChatsPercent}
                color={savedChatsPercent > 90 ? "warning" : "primary"}
                size="sm"
                className="mb-1"
              />
              <div className="flex justify-between text-xs text-default-500">
                <span>
                  {savedChatsPercent >= 90
                    ? "Presque à la limite !"
                    : savedChatsPercent >= 75
                      ? "Approche de la limite"
                      : "Espace disponible"}
                </span>
                <span>{savedChatsPercent}%</span>
              </div>

              {/* Liste des conversations sauvegardées récentes */}
              {savedChats.length > 0 && (
                <div className="mt-2">
                  <div className="mb-1 flex items-center gap-1 text-xs font-medium text-default-600">
                    <MessageSquare size={12} />
                    <span>Conversations récentes</span>
                  </div>
                  <div className="max-h-32 overflow-y-auto rounded-md bg-default-50 p-2">
                    {savedChats.map((chat) => (
                      <div key={chat.id} className="mb-1 text-xs">
                        <div className="truncate font-medium">{chat.title || "Sans titre"}</div>
                        {chat.agentName && <div className="text-[10px] text-default-400">Agent: {chat.agentName}</div>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Agents utilisés */}
          {agentUsageData.maxAgents && (
            <div>
              <div className="mb-1 flex justify-between">
                <span>Agents utilisés</span>
                <span>
                  {agentUsageData.usedAgents}/{agentUsageData.maxAgents}
                </span>
              </div>
              <Progress
                value={agentUsagePercent}
                color={agentUsagePercent > 90 ? "warning" : "primary"}
                size="sm"
                className="mb-1"
              />
              <div className="flex justify-between text-xs text-default-500">
                <span>
                  {agentUsagePercent >= 90
                    ? "Presque à la limite !"
                    : agentUsagePercent >= 75
                      ? "Approche de la limite"
                      : "Agents disponibles"}
                </span>
                <span>{agentUsagePercent}%</span>
              </div>
            </div>
          )}

          {/* Catégories sélectionnées */}
          {categoryUsageData.maxCategories && (
            <div>
              <div className="mb-1 flex justify-between">
                <span>Catégories sélectionnées</span>
                <span>
                  {categoryUsageData.usedCategories}/{categoryUsageData.maxCategories}
                </span>
              </div>
              <Progress
                value={categoryUsagePercent}
                color={categoryUsagePercent > 90 ? "warning" : "primary"}
                size="sm"
                className="mb-1"
              />
              <div className="flex justify-between text-xs text-default-500">
                <span>
                  {categoryUsagePercent >= 90
                    ? "Limite atteinte"
                    : categoryUsagePercent >= 75
                      ? "Presque toutes utilisées"
                      : "Catégories disponibles"}
                </span>
                <span>{categoryUsagePercent}%</span>
              </div>
            </div>
          )}

          {/* Afficher les catégories sélectionnées */}
          {categoryUsageData.maxCategories && selectedCategoryNames.length > 0 && (
            <div className="mt-4">
              <Divider className="my-2" />
              <div className="mb-2 flex items-center">
                <Tag size={16} className="mr-2" />
                <span className="font-medium">Catégories sélectionnées</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedCategoryNames.map((name, index) => (
                  <div key={index} className="rounded-full bg-default-100 px-3 py-1 text-xs">
                    {name}
                  </div>
                ))}
              </div>
              {userSelections?.lastUpdated && (
                <p className="mt-2 text-xs text-default-400">
                  Dernière mise à jour: {new Date(userSelections.lastUpdated).toLocaleDateString()}
                </p>
              )}
            </div>
          )}
        </div>
      </CardBody>
      {categoryUsageData.maxCategories && (
        <CardFooter className="flex justify-end">
          <CategorySelectionButton
            size="sm"
            variant="flat"
            color="primary"
            startContent={<Tag size={16} />}
            isDisabled={!categoryUsageData.canModify}
            title="Gérer mes catégories"
            description="Sélectionnez les catégories qui vous intéressent pour personnaliser votre expérience"
          >
            {categoryUsageData.canModify
              ? "Modifier mes catégories"
              : `Modification possible dans ${categoryUsageData.remainingDays} jours`}
          </CategorySelectionButton>
        </CardFooter>
      )}
    </Card>
  )
}

export default PlanLimitsInfo
