"use client"

import { useEffect, useRef, useState } from "react"
import { useSession } from "next-auth/react"

import { useCategorySelectionModal } from "@/hooks/useCategorySelectionModal"
import { trpc } from "@/lib/trpc/client"

interface CategorySelectionCheckProps {
  forceSelection?: boolean
  onSelectionComplete?: () => void
}

const CategorySelectionCheck: React.FC<CategorySelectionCheckProps> = ({
  forceSelection = false,
  onSelectionComplete,
}) => {
  const { data: session } = useSession()
  // Utiliser un état local pour suivre si le modal a été ouvert
  const [hasCheckedSelection, setHasCheckedSelection] = useState(false)
  // Utiliser une référence pour éviter les ouvertures multiples
  const modalOpenedRef = useRef(false)
  // Utiliser une référence pour suivre si une sélection a été faite avec succès
  const selectionCompletedRef = useRef(false)

  const { openCategorySelectionModal, closeCategorySelectionModal, CategorySelectionModal } = useCategorySelectionModal(
    {
      forceSelection,
      onSuccess: () => {
        // Marquer la sélection comme complétée pour éviter de rouvrir le modal
        selectionCompletedRef.current = true

        // Fermer explicitement le modal même si forceSelection est true
        closeCategorySelectionModal()

        if (onSelectionComplete) {
          onSelectionComplete()
        }
      },
      title: "Personnalisez votre expérience",
      description: "Sélectionnez les catégories qui vous intéressent pour découvrir des agents adaptés à vos besoins",
    }
  )

  // Récupérer les sélections de catégories de l'utilisateur
  const { data: userSelections, isSuccess: userSelectionsLoaded } = trpc.badge.getUserCategorySelections.useQuery(
    undefined,
    {
      enabled: !!session?.user,
      // Éviter de refetch trop souvent
      staleTime: 30000, // 30 secondes
      // Note: gcTime est l'équivalent de cacheTime dans tRPC v10+
      gcTime: 60000, // 1 minute
    }
  )

  // Récupérer les restrictions du plan de l'utilisateur
  const { data: userPlan, isSuccess: userPlanLoaded } = trpc.subscription.getUserActivePlan.useQuery(undefined, {
    enabled: !!session?.user,
    // Éviter de refetch trop souvent
    staleTime: 30000, // 30 secondes
    // Note: gcTime est l'équivalent de cacheTime dans tRPC v10+
    gcTime: 60000, // 1 minute
  })

  // Effet pour vérifier si l'utilisateur doit faire une sélection
  useEffect(() => {
    // Ne pas vérifier si une sélection a déjà été faite avec succès
    if (selectionCompletedRef.current) {
      return
    }

    // Ne vérifier qu'une seule fois et seulement quand toutes les données sont chargées
    if (!hasCheckedSelection && userSelectionsLoaded && userPlanLoaded && !modalOpenedRef.current) {
      // Marquer comme vérifié pour éviter de refaire la vérification
      setHasCheckedSelection(true)

      // Vérifier si l'utilisateur a un plan avec des catégories illimitées
      const categoryLimit = userPlan?.restrictions?.find((r) => r.type === "MAX_CATEGORIES")?.value || null
      const hasUnlimitedCategories = categoryLimit === null

      // Déterminer si l'utilisateur doit faire une sélection
      const hasNoSelections = userSelections?.categoryIds.length === 0 && !hasUnlimitedCategories

      // Vérifier si l'utilisateur a plus de catégories sélectionnées que son plan actuel ne le permet
      const hasTooManySelections =
        !hasUnlimitedCategories && categoryLimit !== null && userSelections?.categoryIds.length > categoryLimit

      // Vérifier si l'utilisateur a changé de plan (en vérifiant si le plan actuel est différent du plan précédent)
      // Nous utilisons une approche simplifiée ici, en supposant que si l'utilisateur a des sélections
      // et que le nombre de catégories autorisées a changé, c'est qu'il a changé de plan
      const hasPlanChanged =
        userSelections?.categoryIds.length > 0 &&
        categoryLimit !== null &&
        userSelections?.categoryIds.length !== categoryLimit

      // L'utilisateur doit faire une sélection s'il n'a pas de sélections, s'il a trop de sélections,
      // ou si son plan a changé
      const needsSelection = hasNoSelections || hasTooManySelections || hasPlanChanged

      // Ouvrir le modal seulement si nécessaire et une seule fois
      if ((needsSelection || forceSelection) && !modalOpenedRef.current) {
        modalOpenedRef.current = true
        // Utiliser setTimeout pour éviter les problèmes de rendu React
        setTimeout(() => {
          openCategorySelectionModal()
        }, 0)
      }
    }
  }, [
    hasCheckedSelection,
    userSelectionsLoaded,
    userPlanLoaded,
    userSelections,
    userPlan,
    forceSelection,
    openCategorySelectionModal,
  ])

  // Renvoyer uniquement le composant modal sans déclencher de re-rendus supplémentaires
  return <CategorySelectionModal />
}

export default CategorySelectionCheck
