"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { passwordSchemaWithRegex } from "@/api/auth/schemas"
import FormField from "@/components/ui/form"
import { WithPasswordStrengthPopoverDr } from "@/components/ui/form.dr"
import { TDictionary } from "@/lib/langs"
import { trpc } from "@/lib/trpc/client"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@nextui-org/button"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { useDisclosure } from "@nextui-org/use-disclosure"

const passwordDictionary: TDictionary<typeof WithPasswordStrengthPopoverDr> = {
  min8Chars: "Au moins 8 caractères",
  containsNumber: "Au moins un chiffre",
  containsLowercase: "Au moins une minuscule",
  containsUppercase: "Au moins une majuscule",
  containsSpecial: "Au moins un caractère spécial",
}

const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, "Le mot de passe actuel est requis"),
    newPassword: passwordSchemaWithRegex(),
    confirmPassword: z.string().min(1, "La confirmation du mot de passe est requise"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirmPassword"],
  })

type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>

export default function ChangePasswordForm() {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [isLoading, setIsLoading] = useState(false)

  const changePasswordMutation = trpc.me.changePassword.useMutation({
    onSuccess: () => {
      toast.success("Votre mot de passe a été modifié avec succès")
      onClose()
    },
    onError: (error) => {
      toast.error(error.message || "Une erreur est survenue lors de la modification du mot de passe")
    },
    onSettled: () => {
      setIsLoading(false)
    },
  })

  const form = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  const onSubmit = (data: ChangePasswordFormValues) => {
    setIsLoading(true)
    changePasswordMutation.mutate({
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
    })
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <>
      <Button color="primary" onPress={onOpen}>
        Changer mon mot de passe
      </Button>

      <Modal isOpen={isOpen} onClose={handleClose} placement="center" backdrop="blur" size="lg">
        <ModalContent>
          {() => (
            <>
              <ModalHeader className="flex flex-col gap-1">Changer mon mot de passe</ModalHeader>
              <ModalBody>
                <form id="change-password-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    form={form}
                    name="currentPassword"
                    label="Mot de passe actuel"
                    type="password"
                    autoComplete="current-password"
                    isDisabled={isLoading}
                  />
                  <FormField
                    form={form}
                    name="newPassword"
                    label="Nouveau mot de passe"
                    type="password-eye-slash"
                    autoComplete="new-password"
                    isDisabled={isLoading}
                    passwordStrength
                    dictionary={passwordDictionary}
                  />
                  <FormField
                    form={form}
                    name="confirmPassword"
                    label="Confirmer le nouveau mot de passe"
                    type="password"
                    autoComplete="new-password"
                    isDisabled={isLoading}
                  />
                </form>
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" onPress={handleClose}>
                  Annuler
                </Button>
                <Button color="primary" type="submit" form="change-password-form" isLoading={isLoading}>
                  Confirmer
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
