"use client"

import React from "react"

import { useCategorySelectionModal } from "@/hooks/useCategorySelectionModal"
import { Button, ButtonProps } from "@nextui-org/button"

interface CategorySelectionButtonProps extends Omit<ButtonProps, "onPress"> {
  onSuccess?: () => void
  title?: string
  description?: string
}

const CategorySelectionButton: React.FC<CategorySelectionButtonProps> = ({
  onSuccess,
  title,
  description,
  children = "Modifier mes catégories",
  ...buttonProps
}) => {
  const { openCategorySelectionModal, CategorySelectionModal } = useCategorySelectionModal({
    onSuccess,
    title,
    description,
  })

  return (
    <>
      <Button {...buttonProps} onPress={openCategorySelectionModal}>
        {children}
      </Button>
      <CategorySelectionModal />
    </>
  )
}

export default CategorySelectionButton
