"use client"

import React, { useEffect, useRef, useState } from "react"
import { useSession } from "next-auth/react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { But<PERSON> } from "@nextui-org/button"
import { Checkbox, CheckboxGroup } from "@nextui-org/checkbox"
import { Modal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Spinner } from "@nextui-org/spinner"

interface CategorySelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
  forceSelection?: boolean // Si true, l'utilisateur ne peut pas fermer le modal sans faire une sélection
  title?: string
  description?: string
}

const CategorySelectionModal: React.FC<CategorySelectionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  forceSelection = false,
  title = "Sélection des catégories",
  description = "Sélectionnez les catégories qui vous intéressent pour personnaliser votre expérience",
}) => {
  const { data: session } = useSession()

  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [originalSelection, setOriginalSelection] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Utiliser les endpoints tRPC du badgeRouter avec des options pour éviter les requêtes trop fréquentes
  const { data: categories } = trpc.badge.getActiveWithAgents.useQuery(undefined, {
    staleTime: 60000, // 1 minute
    gcTime: 300000, // 5 minutes
  })

  const { data: userSelections, isLoading: isLoadingSelections } = trpc.badge.getUserCategorySelections.useQuery(
    undefined,
    {
      enabled: !!session?.user,
      staleTime: 30000, // 30 secondes
      gcTime: 60000, // 1 minute
    }
  )

  const utils = trpc.useUtils()

  const updateCategoriesMutation = trpc.badge.updateUserCategorySelections.useMutation({
    onSuccess: (result) => {
      if (result.success) {
        toast.success("Vos catégories ont été mises à jour avec succès")

        // Mettre à jour la sélection originale
        const newSelection = selectedCategories.map((id) => id.toString())
        setOriginalSelection(newSelection)

        // Invalider les requêtes pour forcer un rechargement des données
        utils.badge.getUserCategorySelections.invalidate()
        utils.badge.getForFilter.invalidate()
        utils.badge.getForFilterAuthenticated.invalidate()
        utils.badge.getActiveWithAgents.invalidate()
        utils.agent.getAll.invalidate()
        utils.agent.getAllAuthenticated.invalidate()

        // Appeler le callback de succès si fourni
        if (onSuccess) {
          onSuccess()
        }

        // Fermer le modal dans tous les cas après une sélection réussie
        onClose()
      } else {
        toast.error(result.message || "Erreur lors de la mise à jour de vos catégories")
        // Réinitialiser la sélection en cas d'erreur
        setSelectedCategories([...originalSelection])
      }
    },
    onError: (error) => {
      console.error("Error saving category selections:", error)
      toast.error("Erreur lors de la sauvegarde de vos catégories")
      setSelectedCategories([...originalSelection])
    },
  })

  // Récupérer les restrictions du plan de l'utilisateur
  const { data: userPlan } = trpc.subscription.getUserActivePlan.useQuery(undefined, {
    enabled: !!session?.user,
    staleTime: 30000, // 30 secondes
    gcTime: 60000, // 1 minute
  })

  // Calculer la limite de catégories basée sur les restrictions du plan
  const categoryLimit = userPlan?.restrictions?.find((r) => r.type === "MAX_CATEGORIES")?.value || null

  // Utiliser une référence pour suivre si les données ont déjà été initialisées
  const dataInitializedRef = useRef(false)

  useEffect(() => {
    // Ne mettre à jour les sélections que si le modal est ouvert, les données sont disponibles,
    // et que nous n'avons pas déjà initialisé les données pour cette session
    if (isOpen && userSelections && userPlan && !dataInitializedRef.current) {
      // Marquer comme initialisé pour éviter les mises à jour en boucle
      dataInitializedRef.current = true

      // Convertir les IDs en strings pour le CheckboxGroup
      const categoryIdsAsStrings = userSelections.categoryIds.map((id) => id.toString())

      // Vérifier si le plan de l'utilisateur a changé
      const hasPlanChanged =
        categoryLimit !== null && categoryIdsAsStrings.length !== 0 && categoryIdsAsStrings.length !== categoryLimit

      // Vérifier si l'utilisateur a plus de catégories sélectionnées que son plan ne le permet
      const hasTooManySelections = categoryLimit !== null && categoryIdsAsStrings.length > categoryLimit

      // Vérifier si l'utilisateur a moins de catégories sélectionnées que son plan ne le permet
      const hasFewerSelections = categoryLimit !== null && categoryIdsAsStrings.length < categoryLimit

      if (hasTooManySelections) {
        // Limiter les catégories au nombre autorisé par le plan, mais seulement pour l'affichage initial
        // Nous ne sélectionnons que les premières catégories pour aider l'utilisateur
        const limitedCategories = categoryIdsAsStrings.slice(0, categoryLimit)
        setSelectedCategories(limitedCategories)

        // Important : nous gardons la sélection originale telle quelle pour que le modal ne se ferme pas
        // et que l'utilisateur puisse faire son choix lui-même
        setOriginalSelection(categoryIdsAsStrings)

        // Informer l'utilisateur que certaines catégories ont été désélectionnées en raison d'un changement de plan
        toast.warning(
          `Votre plan a changé et ne permet que ${categoryLimit} catégories.
          Veuillez sélectionner les catégories que vous souhaitez conserver.`,
          { autoClose: 10000 } // Afficher plus longtemps pour s'assurer que l'utilisateur voit le message
        )

        // Ne pas mettre à jour automatiquement les sélections dans la base de données
        // L'utilisateur doit faire son choix et cliquer sur "Enregistrer"
      } else if (hasFewerSelections && hasPlanChanged) {
        // Cas où l'utilisateur a moins de catégories sélectionnées que son plan ne le permet
        setSelectedCategories(categoryIdsAsStrings)
        setOriginalSelection(categoryIdsAsStrings)

        // Informer l'utilisateur qu'il peut sélectionner plus de catégories
        toast.info(
          `Votre plan a changé et vous permet maintenant de sélectionner jusqu'à ${categoryLimit} catégories.
          Vous pouvez ajouter ${categoryLimit - categoryIdsAsStrings.length} catégories supplémentaires.`,
          { autoClose: 10000 } // Afficher plus longtemps pour s'assurer que l'utilisateur voit le message
        )
      } else {
        // Cas normal, pas de limitation à appliquer
        setSelectedCategories(categoryIdsAsStrings)
        setOriginalSelection(categoryIdsAsStrings)

        // Si le plan a changé mais que le nombre de catégories sélectionnées est correct,
        // informer quand même l'utilisateur du changement
        if (hasPlanChanged) {
          toast.info(`Votre plan a changé. Vous pouvez maintenant sélectionner jusqu'à ${categoryLimit} catégories.`, {
            autoClose: 8000,
          })
        }
      }

      setIsLoading(false)
    }

    // Réinitialiser le flag lorsque le modal se ferme
    if (!isOpen) {
      dataInitializedRef.current = false
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, userSelections, userPlan, categoryLimit])

  const handleSelectionChange = (value: string[]) => {
    // Vérifier si l'utilisateur peut sélectionner plus de catégories
    if (categoryLimit !== null && value.length > categoryLimit) {
      toast.warning(`Vous ne pouvez sélectionner que ${categoryLimit} catégories maximum avec votre plan actuel.`)

      // Au lieu de bloquer complètement la sélection, nous limitons le nombre de catégories
      // en prenant les dernières sélectionnées (pour que l'utilisateur puisse changer ses choix)
      const newSelection = value.slice(-categoryLimit)
      setSelectedCategories(newSelection)
      return
    }

    setSelectedCategories(value)
  }

  const handleSave = async () => {
    if (!session?.user) return

    // Vérifier si l'utilisateur a sélectionné au moins une catégorie lorsque c'est obligatoire
    if (forceSelection && selectedCategories.length === 0 && categoryLimit !== null) {
      toast.error("Vous devez sélectionner au moins une catégorie pour continuer.")
      return
    }

    // Convertir les IDs de string à number
    const categoryIds = selectedCategories.map((id) => parseInt(id, 10))

    // Utiliser la mutation tRPC pour mettre à jour les sélections
    updateCategoriesMutation.mutate({ categoryIds })
  }

  // Vérifier si l'utilisateur a changé sa sélection
  const hasChanges = JSON.stringify(selectedCategories) !== JSON.stringify(originalSelection)

  // Vérifier si l'utilisateur a trop de catégories sélectionnées par rapport à son plan actuel
  const hasTooManyCategories = categoryLimit !== null && originalSelection.length > categoryLimit

  // Le bouton "Enregistrer" doit être activé si l'utilisateur a changé sa sélection
  // ou s'il a trop de catégories sélectionnées (même s'il n'a pas changé sa sélection)
  const shouldEnableSaveButton = hasChanges || hasTooManyCategories

  // L'utilisateur peut fermer le modal s'il n'est pas forcé ou s'il a déjà fait une sélection valide
  const canClose = !forceSelection || (originalSelection.length > 0 && !hasTooManyCategories)

  const isSaving = updateCategoriesMutation.isPending

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        // L'utilisateur ne peut pas fermer le modal en cliquant à l'extérieur ou sur le bouton X
        // si forceSelection est true, mais le modal peut être fermé programmatiquement
        if (canClose) onClose()
      }}
      isDismissable={canClose}
      hideCloseButton={!canClose}
      size="lg"
    >
      <ModalContent>
        {(onModalClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h3 className="text-lg font-bold">{title}</h3>
              <p className="text-sm text-default-500">
                {description}
                {categoryLimit !== null && <span className="ml-1">(maximum {categoryLimit} catégories)</span>}
              </p>
            </ModalHeader>
            <ModalBody>
              {isLoading || isLoadingSelections || !categories ? (
                <div className="flex h-40 items-center justify-center">
                  <Spinner size="lg" />
                </div>
              ) : categories && categories.length > 0 ? (
                <CheckboxGroup value={selectedCategories} onValueChange={handleSelectionChange} className="gap-2">
                  {categories.map((category) => (
                    <Checkbox key={category.id} value={category.id.toString()}>
                      {category.title}
                    </Checkbox>
                  ))}
                </CheckboxGroup>
              ) : (
                <p className="text-center text-default-500">Aucune catégorie disponible</p>
              )}

              {categoryLimit !== null && (
                <div className="mt-2 text-right text-sm text-default-500">
                  {selectedCategories.length}/{categoryLimit} catégories sélectionnées
                </div>
              )}

              {userSelections?.lastUpdated && (
                <p className="mt-4 text-xs text-default-400">
                  Dernière mise à jour: {new Date(userSelections.lastUpdated).toLocaleDateString()}
                </p>
              )}

              <p className="mt-2 text-xs text-default-400">
                Vous ne pourrez modifier vos catégories qu&apos;une fois tous les 30 jours.
              </p>
            </ModalBody>
            <ModalFooter>
              {!forceSelection && (
                <Button
                  variant="light"
                  onPress={() => {
                    if (canClose) onModalClose()
                  }}
                >
                  Annuler
                </Button>
              )}
              <Button
                color="primary"
                isDisabled={forceSelection ? selectedCategories.length === 0 : !shouldEnableSaveButton}
                isLoading={isSaving}
                onPress={handleSave}
              >
                Enregistrer
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}

export default CategorySelectionModal
