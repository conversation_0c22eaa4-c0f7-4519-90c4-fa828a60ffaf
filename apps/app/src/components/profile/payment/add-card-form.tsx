"use client"

import React, { useMemo, useState } from "react"
import { usePaymentInputs } from "react-payment-inputs"
import images from "react-payment-inputs/images"
import { toast } from "react-toastify"

import { registerCard } from "@/actions/mangopay-actions"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { cn } from "@nextui-org/theme"

type CardData = {
  cardNumber: string
  expiryDate: string
  cvc: string
}

interface AddCardFormProps {
  onSuccess: () => void
}

const errorMessagesFr = {
  emptyCardNumber: "Le numéro de carte est requis.",
  invalidCardNumber: "Le numéro de carte est invalide.",
  emptyExpiryDate: "La date d'expiration est requise.",
  monthOutOfRange: "Le mois d'expiration doit être compris entre 01 et 12.",
  yearOutOfRange: "L'année d'expiration ne peut pas être dans le passé.",
  dateOutOfRange: "La date d'expiration ne peut pas être dans le passé.",
  invalidExpiryDate: "La date d'expiration est invalide.",
  emptyCVC: "Le CVC est requis.",
  invalidCVC: "Le CVC est invalide.",
  unsupportedCardType:
    "Ce type de carte n'est pas accepté. Veuillez utiliser CB, Visa, Mastercard, American Express, Maestro ou Bancontact.",
}

const SUPPORTED_LIBRARY_CARD_TYPES = ["cb", "visa", "mastercard", "amex", "maestro", "bancontact"] as const

type LibraryCardType = (typeof SUPPORTED_LIBRARY_CARD_TYPES)[number]

function isSupportedLibraryCardType(type: string): type is LibraryCardType {
  return (SUPPORTED_LIBRARY_CARD_TYPES as readonly string[]).includes(type)
}

type TargetCardType = "CB_VISA_MASTERCARD" | "AMEX" | "MAESTRO" | "BCMC"

function mapLibraryTypeToTargetType(libraryCardType: LibraryCardType): TargetCardType {
  switch (libraryCardType) {
    case "cb":
    case "visa":
    case "mastercard":
      return "CB_VISA_MASTERCARD"
    case "amex":
      return "AMEX"
    case "maestro":
      return "MAESTRO"
    case "bancontact":
      return "BCMC"
  }
}

const AddCardForm: React.FC<AddCardFormProps> = ({ onSuccess }) => {
  const [cardData, setCardData] = useState<CardData>({
    cardNumber: "",
    expiryDate: "",
    cvc: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [serverError, setServerError] = useState<string | null>(null)
  const [cardValidationError, setCardValidationError] = useState<string | null>(null)

  const { meta, getCardNumberProps, getExpiryDateProps, getCVCProps, getCardImageProps } = usePaymentInputs({
    errorMessages: errorMessagesFr,
  })

  const CardIcon = useMemo(() => {
    //@ts-expect-error type of import not found
    const props = getCardImageProps({ images })
    props.width = "35"
    props.height = "auto"
    return <svg {...props} className="pt-4" />
  }, [getCardImageProps])

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, cardNumber: e.target.value }))
    if (cardValidationError) setCardValidationError(null)
    if (serverError) setServerError(null)
  }

  const handleExpiryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, expiryDate: e.target.value }))
    if (serverError) setServerError(null)
  }

  const handleCvcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardData((prev) => ({ ...prev, cvc: e.target.value }))
    if (serverError) setServerError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setCardValidationError(null)
    setServerError(null)

    if (meta.error) {
      const errorMessage =
        meta.erroredInputs.cardNumber ||
        meta.erroredInputs.expiryDate ||
        meta.erroredInputs.cvc ||
        "Erreur de validation de la carte."
      setCardValidationError(errorMessage)
      toast.warning(errorMessage)
      return
    }

    if (!meta.cardType || !meta.cardType.type) {
      const message = "Type de carte non détecté. Veuillez vérifier le numéro."
      setCardValidationError(message)
      toast.warning(message)
      return
    }

    if (!isSupportedLibraryCardType(meta.cardType.type)) {
      const message = errorMessagesFr.unsupportedCardType
      setCardValidationError(message)
      toast.error(message)
      return
    }

    const targetCardType = mapLibraryTypeToTargetType(meta.cardType.type)

    setIsLoading(true)
    try {
      const formattedExpiryDate = cardData.expiryDate.replace(/\s*\/\s*/, "")
      const formattedCardNumber = cardData.cardNumber.replace(/\s/g, "")

      await registerCard({
        cardNumber: formattedCardNumber,
        cardExpirationDate: formattedExpiryDate,
        cardCvx: cardData.cvc,
        cardType: targetCardType,
      })

      onSuccess()
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erreur lors de l'enregistrement de la carte."
      setServerError(message)
      toast.error(message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {serverError && <div className="rounded-md bg-danger-50 p-3 text-sm text-danger">{serverError}</div>}

      {meta.cardType && (
        <p
          className={cn(
            "mb-2 text-sm",
            meta.cardType.type && !isSupportedLibraryCardType(meta.cardType.type) ? "text-warning" : "text-default-600"
          )}
        >
          Type de carte détecté : {meta.cardType.displayName}
          {meta.cardType.type && !isSupportedLibraryCardType(meta.cardType.type) && " (Non supporté)"}
        </p>
      )}

      <Input
        {...getCardNumberProps({ onChange: handleCardNumberChange, value: cardData.cardNumber })}
        label="Numéro de carte"
        placeholder="0000 0000 0000 0000"
        isRequired
        variant="bordered"
        startContent={CardIcon}
        isInvalid={(meta.touchedInputs?.cardNumber && !!meta.erroredInputs?.cardNumber) || !!cardValidationError}
        errorMessage={(meta.touchedInputs?.cardNumber && meta.erroredInputs.cardNumber) || cardValidationError}
        aria-label="Numéro de carte"
        className="mb-4"
      />

      <div className="grid grid-cols-2 gap-4">
        <Input
          //@ts-expect-error type of import not found
          {...getExpiryDateProps({ onChange: handleExpiryDateChange, value: cardData.expiryDate })}
          label="Date d'expiration"
          placeholder="MM / YY"
          isRequired
          variant="bordered"
          isInvalid={meta.touchedInputs?.expiryDate && !!meta.erroredInputs?.expiryDate}
          errorMessage={meta.touchedInputs?.expiryDate && meta.erroredInputs.expiryDate}
          aria-label="Date d'expiration MM/YY"
        />

        <Input
          //@ts-expect-error type of import not found
          {...getCVCProps({ onChange: handleCvcChange, value: cardData.cvc })}
          label="CVC"
          placeholder="123"
          isRequired
          variant="bordered"
          isInvalid={meta.touchedInputs?.cvc && !!meta.erroredInputs?.cvc}
          errorMessage={meta.touchedInputs?.cvc && meta.erroredInputs.cvc}
          aria-label="Code CVC de sécurité"
        />
      </div>

      <Button
        type="submit"
        color="primary"
        className="w-full"
        isLoading={isLoading}
        isDisabled={isLoading || !!meta.error || !!cardValidationError}
      >
        Ajouter cette carte
      </Button>
    </form>
  )
}

export default AddCardForm
