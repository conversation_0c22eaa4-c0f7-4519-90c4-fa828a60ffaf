"use client"

import React, { useState } from "react"
import { Plus, Trash2 } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Mo<PERSON>, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Spinner } from "@nextui-org/spinner"

import AddCardForm from "./add-card-form"

const UserPaymentCards = () => {
  const [isAddCardModalOpen, setIsAddCardModalOpen] = useState(false)
  const [cardToDelete, setCardToDelete] = useState<string | null>(null)

  // Query to fetch user's payment cards
  const { data: cards, isLoading, refetch } = trpc.card.getUserCards.useQuery()

  // Mutations
  const deleteCard = trpc.card.delete.useMutation({
    onSuccess: () => {
      toast.success("Carte supprimée avec succès")
      refetch()
    },
    onError: (error) => {
      toast.error(`Erreur: ${error.message}`)
    },
  })

  const setDefaultCard = trpc.card.setDefault.useMutation({
    onSuccess: () => {
      toast.success("Carte définie comme principale")
      refetch()
    },
    onError: (error) => {
      toast.error(`Erreur: ${error.message}`)
    },
  })

  const handleDeleteCard = (cardId: string) => {
    if (cards && cards.length <= 1) {
      toast.error("Vous devez conserver au moins une carte de paiement")
      return
    }
    setCardToDelete(cardId)
  }

  const confirmDeleteCard = () => {
    if (cardToDelete) {
      deleteCard.mutate(cardToDelete)
      setCardToDelete(null)
    }
  }

  const handleSetDefaultCard = (cardId: string) => {
    setDefaultCard.mutate({ cardId })
  }

  const handleCardAdded = () => {
    setIsAddCardModalOpen(false)
    refetch()
  }

  return (
    <div className="space-y-4">
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Spinner label="Chargement des cartes..." />
        </div>
      ) : (
        <>
          {cards && cards.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {cards.map((card) => (
                <Card key={card.id} className={`border ${card.isDefault ? "border-primary" : "border-default-200"}`}>
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {/* {getCardIcon(card.cardType)} */}
                        <div>
                          <p className="font-medium">•••• {card.last4}</p>
                          <p className="text-sm text-default-500">
                            Expire: {card.expirationDate?.slice(0, 2) + "/" + card.expirationDate?.slice(2, 4)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!card.isDefault && (
                          <>
                            <Button
                              size="sm"
                              variant="flat"
                              color="primary"
                              onPress={() => handleSetDefaultCard(card.id)}
                            >
                              Définir par défaut
                            </Button>
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              color="danger"
                              onPress={() => handleDeleteCard(card.id)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </>
                        )}
                        {card.isDefault && (
                          <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                            Principale
                          </span>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-dashed border-default-300 p-8 text-center">
              <p className="mb-4 text-default-500">Aucune carte de paiement enregistrée</p>
            </div>
          )}

          <Button
            color="primary"
            variant="flat"
            startContent={<Plus size={16} />}
            onPress={() => setIsAddCardModalOpen(true)}
          >
            Ajouter une carte de paiement
          </Button>
        </>
      )}

      {/* Modal d'ajout de carte */}
      <Modal isOpen={isAddCardModalOpen} onClose={() => setIsAddCardModalOpen(false)} size="lg">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">Ajouter une carte de paiement</h3>
          </ModalHeader>
          <ModalBody>
            <AddCardForm onSuccess={handleCardAdded} />
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Modal de confirmation de suppression */}
      <Modal isOpen={Boolean(cardToDelete)} onClose={() => setCardToDelete(null)} size="sm">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">Confirmer la suppression</h3>
          </ModalHeader>
          <ModalBody>
            <p>Êtes-vous sûr de vouloir supprimer cette carte de paiement ?</p>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={() => setCardToDelete(null)}>
              Annuler
            </Button>
            <Button color="danger" onPress={confirmDeleteCard} isLoading={deleteCard.isPending}>
              Supprimer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  )
}

export default UserPaymentCards
