"use client"

import React, { useState } from "react"
import { motion } from "framer-motion"
import { BarChart3, Download, Users, MessageSquare, TrendingUp, Calendar } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { DateRangePicker } from "@nextui-org/date-picker"
import { Divider } from "@nextui-org/divider"
import { Progress } from "@nextui-org/progress"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@nextui-org/table"

interface FormAnalyticsDashboardProps {
  formId: string
}

export const FormAnalyticsDashboard: React.FC<FormAnalyticsDashboardProps> = ({ formId }) => {
  const [dateRange, setDateRange] = useState<{from?: Date; to?: Date}>({})
  const [selectedTimeframe, setSelectedTimeframe] = useState("all")

  // Fetch form analytics
  const { data: analytics, isLoading: analyticsLoading } = trpc.form.submission.getAnalytics.useQuery({
    formId,
    dateFrom: dateRange.from,
    dateTo: dateRange.to,
  })

  // Fetch form submissions
  const { data: submissions, isLoading: submissionsLoading } = trpc.form.submission.getByForm.useQuery({
    formId,
    page: 1,
    pageSize: 10,
  })

  const handleExportData = () => {
    // TODO: Implement data export functionality
    toast.info("Export des données - À implémenter")
  }

  const handleTimeframeChange = (timeframe: string) => {
    setSelectedTimeframe(timeframe)
    const now = new Date()
    
    switch (timeframe) {
      case "7d":
        setDateRange({
          from: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
          to: now,
        })
        break
      case "30d":
        setDateRange({
          from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
          to: now,
        })
        break
      case "90d":
        setDateRange({
          from: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
          to: now,
        })
        break
      default:
        setDateRange({})
    }
  }

  if (analyticsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Spinner size="lg" />
        <p className="ml-4">Chargement des analytics...</p>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center p-8">
        <p className="text-default-500">Aucune donnée d'analytics disponible</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics - {analytics.form.title}</h2>
          <p className="text-default-500">
            Analysez les réponses et les tendances de votre formulaire
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select
            placeholder="Période"
            selectedKeys={[selectedTimeframe]}
            onSelectionChange={(keys) => handleTimeframeChange(Array.from(keys)[0] as string)}
            className="w-40"
          >
            <SelectItem key="all">Toute la période</SelectItem>
            <SelectItem key="7d">7 derniers jours</SelectItem>
            <SelectItem key="30d">30 derniers jours</SelectItem>
            <SelectItem key="90d">90 derniers jours</SelectItem>
          </Select>
          <Button
            color="primary"
            startContent={<Download className="w-4 h-4" />}
            onPress={handleExportData}
          >
            Exporter
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardBody className="flex flex-row items-center gap-4">
              <div className="p-3 bg-primary-100 rounded-lg">
                <Users className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-default-500">Total Soumissions</p>
                <p className="text-2xl font-bold">{analytics.totalSubmissions}</p>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardBody className="flex flex-row items-center gap-4">
              <div className="p-3 bg-success-100 rounded-lg">
                <MessageSquare className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-default-500">Questions</p>
                <p className="text-2xl font-bold">{analytics.questionAnalytics.length}</p>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardBody className="flex flex-row items-center gap-4">
              <div className="p-3 bg-warning-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-sm text-default-500">Taux de réponse</p>
                <p className="text-2xl font-bold">
                  {analytics.totalSubmissions > 0 ? "100%" : "0%"}
                </p>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardBody className="flex flex-row items-center gap-4">
              <div className="p-3 bg-secondary-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-secondary" />
              </div>
              <div>
                <p className="text-sm text-default-500">Type</p>
                <p className="text-lg font-semibold">
                  {analytics.form.type === "SUBSCRIPTION_CANCELLATION" 
                    ? "Annulation" 
                    : analytics.form.type}
                </p>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Question Analytics */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Analyse par question</h3>
        </CardHeader>
        <CardBody className="space-y-6">
          {analytics.questionAnalytics.map((questionAnalytic, index) => (
            <motion.div
              key={questionAnalytic.questionId}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="space-y-3"
            >
              <div className="flex items-center justify-between">
                <h4 className="font-medium">{questionAnalytic.questionTitle}</h4>
                <Chip size="sm" variant="flat">
                  {questionAnalytic.totalResponses} réponses
                </Chip>
              </div>

              {/* Question-specific analytics */}
              {questionAnalytic.questionType === "YES_NO" && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Oui</span>
                    <span className="text-sm font-medium">{questionAnalytic.yesCount || 0}</span>
                  </div>
                  <Progress
                    value={questionAnalytic.totalResponses > 0 
                      ? ((questionAnalytic.yesCount || 0) / questionAnalytic.totalResponses) * 100 
                      : 0}
                    color="success"
                    className="w-full"
                  />
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Non</span>
                    <span className="text-sm font-medium">{questionAnalytic.noCount || 0}</span>
                  </div>
                  <Progress
                    value={questionAnalytic.totalResponses > 0 
                      ? ((questionAnalytic.noCount || 0) / questionAnalytic.totalResponses) * 100 
                      : 0}
                    color="danger"
                    className="w-full"
                  />
                </div>
              )}

              {(questionAnalytic.questionType === "SINGLE_CHOICE" || 
                questionAnalytic.questionType === "MULTIPLE_CHOICE") && 
                questionAnalytic.optionCounts && (
                <div className="space-y-2">
                  {Object.entries(questionAnalytic.optionCounts).map(([option, count]) => (
                    <div key={option} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">{option}</span>
                        <span className="text-sm font-medium">{count}</span>
                      </div>
                      <Progress
                        value={questionAnalytic.totalResponses > 0 
                          ? (count / questionAnalytic.totalResponses) * 100 
                          : 0}
                        color="primary"
                        className="w-full"
                      />
                    </div>
                  ))}
                </div>
              )}

              {(questionAnalytic.questionType === "RATING" || 
                questionAnalytic.questionType === "NUMBER") && (
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-default-500">Moyenne:</span>
                    <span className="ml-2 font-medium">
                      {questionAnalytic.average?.toFixed(2) || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-default-500">Min:</span>
                    <span className="ml-2 font-medium">{questionAnalytic.min || "N/A"}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Max:</span>
                    <span className="ml-2 font-medium">{questionAnalytic.max || "N/A"}</span>
                  </div>
                </div>
              )}

              {(questionAnalytic.questionType === "TEXT_SHORT" || 
                questionAnalytic.questionType === "TEXT_LONG") && (
                <div className="text-sm">
                  <span className="text-default-500">Longueur moyenne:</span>
                  <span className="ml-2 font-medium">
                    {questionAnalytic.averageLength?.toFixed(0) || "0"} caractères
                  </span>
                </div>
              )}

              {index < analytics.questionAnalytics.length - 1 && <Divider />}
            </motion.div>
          ))}
        </CardBody>
      </Card>

      {/* Recent Submissions */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Soumissions récentes</h3>
        </CardHeader>
        <CardBody>
          {submissionsLoading ? (
            <div className="flex items-center justify-center p-4">
              <Spinner />
            </div>
          ) : submissions?.data.length ? (
            <Table aria-label="Soumissions récentes">
              <TableHeader>
                <TableColumn>UTILISATEUR</TableColumn>
                <TableColumn>DATE</TableColumn>
                <TableColumn>ABONNEMENT</TableColumn>
                <TableColumn>RÉPONSES</TableColumn>
              </TableHeader>
              <TableBody>
                {submissions.data.map((submission) => (
                  <TableRow key={submission.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{submission.user?.name || "Utilisateur anonyme"}</p>
                        <p className="text-sm text-default-500">{submission.user?.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {submission.submittedAt 
                        ? new Date(submission.submittedAt).toLocaleDateString("fr-FR")
                        : "N/A"}
                    </TableCell>
                    <TableCell>
                      {submission.subscription ? (
                        <Chip size="sm" variant="flat">
                          {submission.subscription.plan.name}
                        </Chip>
                      ) : (
                        "N/A"
                      )}
                    </TableCell>
                    <TableCell>{submission.responses.length}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center text-default-500 p-4">Aucune soumission trouvée</p>
          )}
        </CardBody>
      </Card>
    </div>
  )
}
