"use client"

import React from "react"
import { Plus, Trash2, <PERSON>, <PERSON>Off } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card"
import { Select, SelectItem } from "@nextui-org/select"
import { Input } from "@nextui-org/input"
import { Switch } from "@nextui-org/switch"
import { Chip } from "@nextui-org/chip"

import type {
  ShowConditions,
  ConditionalLogicGroup,
  ConditionalLogicRule,
  ConditionalOperator
} from "@/types/conditional-logic"
import { getAvailableOperators, getOperatorDisplayName } from "@/types/conditional-logic"

interface ConditionalLogicEditorProps {
  showConditions?: ShowConditions | null
  onChange: (conditions: ShowConditions | null) => void
  availableQuestions: Array<{
    id: string
    title: string
    type: string
    order: number
  }>
  currentQuestionOrder: number
}

export const ConditionalLogicEditor: React.FC<ConditionalLogicEditorProps> = ({
  showConditions,
  onChange,
  availableQuestions,
  currentQuestionOrder,
}) => {
  const [isEnabled, setIsEnabled] = React.useState(!!showConditions)

  // Filter questions that come before the current question
  const previousQuestions = availableQuestions.filter(q => q.order < currentQuestionOrder)

  const handleToggle = (enabled: boolean) => {
    setIsEnabled(enabled)
    if (!enabled) {
      onChange(null)
    } else {
      onChange({
        groups: [{
          id: crypto.randomUUID(),
          rules: [],
          logic: "AND"
        }],
        groupLogic: "AND"
      })
    }
  }

  const addGroup = () => {
    if (!showConditions) return

    const newGroup: ConditionalLogicGroup = {
      id: crypto.randomUUID(),
      rules: [],
      logic: "AND"
    }

    onChange({
      ...showConditions,
      groups: [...showConditions.groups, newGroup]
    })
  }

  const removeGroup = (groupId: string) => {
    if (!showConditions) return

    onChange({
      ...showConditions,
      groups: showConditions.groups.filter(g => g.id !== groupId)
    })
  }

  const updateGroup = (groupId: string, updates: Partial<ConditionalLogicGroup>) => {
    if (!showConditions) return

    onChange({
      ...showConditions,
      groups: showConditions.groups.map(g =>
        g.id === groupId ? { ...g, ...updates } : g
      )
    })
  }

  const addRule = (groupId: string) => {
    if (!showConditions || previousQuestions.length === 0) return

    const newRule: ConditionalLogicRule = {
      id: crypto.randomUUID(),
      questionId: previousQuestions[0].id,
      operator: "equals",
      value: ""
    }

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group) return

    updateGroup(groupId, {
      rules: [...group.rules, newRule]
    })
  }

  const removeRule = (groupId: string, ruleId: string) => {
    if (!showConditions) return

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group) return

    updateGroup(groupId, {
      rules: group.rules.filter(r => r.id !== ruleId)
    })
  }

  const updateRule = (groupId: string, ruleId: string, updates: Partial<ConditionalLogicRule>) => {
    if (!showConditions) return

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group) return

    updateGroup(groupId, {
      rules: group.rules.map(r =>
        r.id === ruleId ? { ...r, ...updates } : r
      )
    })
  }

  if (previousQuestions.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <EyeOff className="w-8 h-8 text-default-400 mx-auto mb-2" />
          <p className="text-default-500">
            La logique conditionnelle n'est disponible que pour les questions qui ont des questions précédentes.
          </p>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <Eye className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Logique conditionnelle</h3>
        </div>
        <Switch
          isSelected={isEnabled}
          onValueChange={handleToggle}
          size="sm"
        >
          {isEnabled ? "Activée" : "Désactivée"}
        </Switch>
      </CardHeader>

      {isEnabled && (
        <CardBody className="space-y-4">
          <p className="text-sm text-default-600">
            Cette question sera affichée uniquement si les conditions suivantes sont remplies :
          </p>

          {showConditions && (
            <div className="space-y-4">
              {/* Group Logic Selector */}
              {showConditions.groups?.length > 1 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Combiner les groupes avec :</span>
                  <Select
                    size="sm"
                    selectedKeys={[showConditions.groupLogic]}
                    onSelectionChange={(keys) => {
                      const logic = Array.from(keys)[0] as "AND" | "OR"
                      onChange({ ...showConditions, groupLogic: logic })
                    }}
                    className="w-24"
                  >
                    <SelectItem key="AND" value="AND">ET</SelectItem>
                    <SelectItem key="OR" value="OR">OU</SelectItem>
                  </Select>
                </div>
              )}

              {/* Groups */}
              {showConditions.groups.map((group, groupIndex) => (
                <Card key={group.id} className="border border-default-200">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center w-full">
                      <Chip size="sm" variant="flat">
                        Groupe {groupIndex + 1}
                      </Chip>
                      <div className="flex items-center gap-2">
                        {group.rules.length > 1 && (
                          <Select
                            size="sm"
                            selectedKeys={[group.logic]}
                            onSelectionChange={(keys) => {
                              const logic = Array.from(keys)[0] as "AND" | "OR"
                              updateGroup(group.id, { logic })
                            }}
                            className="w-20"
                          >
                            <SelectItem key="AND" value="AND">ET</SelectItem>
                            <SelectItem key="OR" value="OR">OU</SelectItem>
                          </Select>
                        )}
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color="danger"
                          onPress={() => removeGroup(group.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody className="space-y-3">
                    {/* Rules */}
                    {group.rules.map((rule, ruleIndex) => (
                      <RuleEditor
                        key={rule.id}
                        rule={rule}
                        ruleIndex={ruleIndex}
                        previousQuestions={previousQuestions}
                        onUpdate={(updates) => updateRule(group.id, rule.id, updates)}
                        onRemove={() => removeRule(group.id, rule.id)}
                      />
                    ))}

                    <Button
                      size="sm"
                      variant="flat"
                      startContent={<Plus className="w-4 h-4" />}
                      onPress={() => addRule(group.id)}
                    >
                      Ajouter une règle
                    </Button>
                  </CardBody>
                </Card>
              ))}

              <Button
                size="sm"
                variant="bordered"
                startContent={<Plus className="w-4 h-4" />}
                onPress={addGroup}
              >
                Ajouter un groupe
              </Button>
            </div>
          )}
        </CardBody>
      )}
    </Card>
  )
}

interface RuleEditorProps {
  rule: ConditionalLogicRule
  ruleIndex: number
  previousQuestions: Array<{
    id: string
    title: string
    type: string
  }>
  onUpdate: (updates: Partial<ConditionalLogicRule>) => void
  onRemove: () => void
}

const RuleEditor: React.FC<RuleEditorProps> = ({
  rule,
  ruleIndex,
  previousQuestions,
  onUpdate,
  onRemove,
}) => {
  const selectedQuestion = previousQuestions.find(q => q.id === rule.questionId)
  const availableOperators = selectedQuestion ? getAvailableOperators(selectedQuestion.type) : []

  return (
    <div className="flex items-center gap-2 p-3 bg-default-50 rounded">
      <Chip size="sm" variant="flat" color="primary">
        {ruleIndex + 1}
      </Chip>

      {/* Question Selector */}
      <Select
        size="sm"
        placeholder="Question"
        selectedKeys={rule.questionId ? [rule.questionId] : []}
        onSelectionChange={(keys) => {
          const questionId = Array.from(keys)[0] as string
          onUpdate({ questionId })
        }}
        className="flex-1"
      >
        {previousQuestions.map((question) => (
          <SelectItem key={question.id} value={question.id}>
            {question.title}
          </SelectItem>
        ))}
      </Select>

      {/* Operator Selector */}
      <Select
        size="sm"
        placeholder="Opérateur"
        selectedKeys={rule.operator ? [rule.operator] : []}
        onSelectionChange={(keys) => {
          const operator = Array.from(keys)[0] as ConditionalOperator
          onUpdate({ operator })
        }}
        className="w-48"
      >
        {availableOperators.map((operator) => (
          <SelectItem key={operator} value={operator}>
            {getOperatorDisplayName(operator)}
          </SelectItem>
        ))}
      </Select>

      {/* Value Input */}
      {rule.operator && !["is_empty", "is_not_empty"].includes(rule.operator) && (
        <Input
          size="sm"
          placeholder="Valeur"
          value={String(rule.value || "")}
          onValueChange={(value) => onUpdate({ value })}
          className="flex-1"
        />
      )}

      <Button
        isIconOnly
        size="sm"
        variant="light"
        color="danger"
        onPress={onRemove}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  )
}
