"use client"

import React, { use<PERSON><PERSON>back, useState } from "react"
import { <PERSON><PERSON><PERSON>3, <PERSON>, <PERSON>, Pause, Play, Plus, Trash2 } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"
import { FormStatus, FormType } from "@prisma/client"


interface FormData {
  id: string
  title: string
  description?: string | null
  type: FormType
  status: FormStatus
  isActive: boolean
  version: number
  showProgressBar: boolean
  allowSaveProgress: boolean
  requireAuth: boolean
  enableConditionalLogic: boolean
  creator: {
    id: string
    name?: string | null
    email?: string | null
  }
  questions: Array<{
    id: string
    title: string
    description?: string | null
    type: string
    order: number
    isRequired: boolean
  }>
  _count: {
    submissions: number
  }
  createdAt: string
  updatedAt: string
}

interface FormsTableProps {
  initialForms: FormData[]
  initialPagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

export const FormsTable: React.FC<FormsTableProps> = ({ initialForms, initialPagination }) => {
  const [forms, setForms] = useState(initialForms)
  const [pagination, setPagination] = useState(initialPagination)
  const [page] = useState(1)
  const [pageSize] = useState(15)
  const [selectedForm, setSelectedForm] = useState<FormData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const utils = trpc.useUtils()

  // Load forms data
  const loadData = useCallback(async () => {
    try {
      const result = await utils.form.getAll.fetch({
        page,
        pageSize,
      })
      setForms(result.data)
      setPagination(result.pagination)
    } catch (error) {
      toast.error("Erreur lors du chargement des formulaires")
    }
  }, [page, pageSize, utils.form.getAll])

  // Delete form mutation
  const deleteForm = trpc.form.delete.useMutation({
    onSuccess: () => {
      toast.success("Formulaire supprimé avec succès")
      loadData()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la suppression")
    },
  })

  // Activate form mutation
  const activateForm = trpc.form.activate.useMutation({
    onSuccess: () => {
      toast.success("Formulaire activé avec succès")
      loadData()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de l'activation")
    },
  })

  // Deactivate form mutation
  const deactivateForm = trpc.form.deactivate.useMutation({
    onSuccess: () => {
      toast.success("Formulaire désactivé avec succès")
      loadData()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la désactivation")
    },
  })

  const getStatusColor = (status: FormStatus) => {
    switch (status) {
      case FormStatus.ACTIVE:
        return "success"
      case FormStatus.INACTIVE:
        return "warning"
      case FormStatus.DRAFT:
        return "default"
      case FormStatus.ARCHIVED:
        return "danger"
      default:
        return "default"
    }
  }

  const getStatusLabel = (status: FormStatus) => {
    switch (status) {
      case FormStatus.ACTIVE:
        return "Actif"
      case FormStatus.INACTIVE:
        return "Inactif"
      case FormStatus.DRAFT:
        return "Brouillon"
      case FormStatus.ARCHIVED:
        return "Archivé"
      default:
        return status
    }
  }

  const getTypeLabel = (type: FormType) => {
    switch (type) {
      case FormType.SUBSCRIPTION_CANCELLATION:
        return "Annulation d'abonnement"
      case FormType.GENERAL_FEEDBACK:
        return "Feedback général"
      case FormType.SUPPORT_REQUEST:
        return "Demande de support"
      default:
        return type
    }
  }

  const handlePreview = (form: FormData) => {
    setSelectedForm(form)
    setIsPreviewOpen(true)
  }

  const handleDelete = (formId: string) => {
    // TODO: Replace with NextUI confirmation dialog
    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce formulaire ?")) {
      deleteForm.mutate(formId)
    }
  }

  const handleToggleActive = (form: FormData) => {
    if (form.isActive) {
      deactivateForm.mutate(form.id)
    } else {
      activateForm.mutate(form.id)
    }
  }

  return (
    <>
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Formulaires</h3>
            <p className="text-sm text-default-500">
              {pagination.total} formulaire{pagination.total > 1 ? "s" : ""} au total
            </p>
          </div>
          <Button
            color="primary"
            startContent={<Plus className="size-4" />}
            href="/admin/forms/create"
            as="a"
          >
            Nouveau formulaire
          </Button>
        </CardHeader>
        <CardBody>
          <Table aria-label="Formulaires">
            <TableHeader>
              <TableColumn>TITRE</TableColumn>
              <TableColumn>TYPE</TableColumn>
              <TableColumn>STATUT</TableColumn>
              <TableColumn>QUESTIONS</TableColumn>
              <TableColumn>SOUMISSIONS</TableColumn>
              <TableColumn>CRÉÉ PAR</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {forms.map((form) => (
                <TableRow key={form.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{form.title}</p>
                      {form.description && (
                        <p className="text-sm text-default-500 truncate max-w-xs">
                          {form.description}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Chip size="sm" variant="flat">
                      {getTypeLabel(form.type)}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Chip
                        size="sm"
                        color={getStatusColor(form.status)}
                        variant="flat"
                      >
                        {getStatusLabel(form.status)}
                      </Chip>
                      {form.isActive && (
                        <Chip size="sm" color="success" variant="dot">
                          Actif
                        </Chip>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{form.questions.length}</TableCell>
                  <TableCell>{form._count.submissions}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{form.creator.name || "Utilisateur"}</p>
                      <p className="text-default-500">{form.creator.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Tooltip content="Aperçu">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          onPress={() => handlePreview(form)}
                        >
                          <Eye className="size-4" />
                        </Button>
                      </Tooltip>

                      <Tooltip content="Modifier">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          href={`/admin/forms/${form.id}/edit`}
                          as="a"
                        >
                          <Edit className="size-4" />
                        </Button>
                      </Tooltip>

                      <Tooltip content={form.isActive ? "Désactiver" : "Activer"}>
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color={form.isActive ? "warning" : "success"}
                          onPress={() => handleToggleActive(form)}
                          isLoading={activateForm.isPending || deactivateForm.isPending}
                        >
                          {form.isActive ? (
                            <Pause className="size-4" />
                          ) : (
                            <Play className="size-4" />
                          )}
                        </Button>
                      </Tooltip>

                      <Tooltip content="Statistiques">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          href={`/admin/forms/${form.id}/analytics`}
                          as="a"
                        >
                          <BarChart3 className="size-4" />
                        </Button>
                      </Tooltip>

                      <Tooltip content="Supprimer">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color="danger"
                          onPress={() => handleDelete(form.id)}
                          isLoading={deleteForm.isPending}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </Tooltip>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Form Preview Modal - TODO: Import FormPreviewModal component */}
      {isPreviewOpen && selectedForm && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">Aperçu: {selectedForm.title}</h3>
                <button
                  onClick={() => setIsPreviewOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-4">
                {selectedForm.questions.map((question, index) => (
                  <div key={question.id} className="p-4 border rounded-lg">
                    <h4 className="font-medium">
                      Question {index + 1}: {question.title}
                      {question.isRequired && <span className="text-red-500 ml-1">*</span>}
                    </h4>
                    {question.description && (
                      <p className="text-sm text-gray-600 mt-1">{question.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-2">Type: {question.type}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
