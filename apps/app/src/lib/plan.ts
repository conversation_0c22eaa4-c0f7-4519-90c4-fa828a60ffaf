"use server"
import { PlanRestriction } from "@/api/plan/_router"
import { DAYS_BEFORE_CATEGORY_CHANGE, DEFAULT_PLAN_RESTRICTIONS } from "@/constants/restrictions"
import { prisma } from "@/lib/prisma"

export async function getUserPlanRestrictions(userId: string): Promise<PlanRestriction[]> {
  try {
    const activeSubscription = await prisma.subscription.findFirst({
      where: {
        userId: userId,
        status: "ACTIVE",
      },
      include: {
        plan: {
          include: {
            restrictions: true,
          },
        },
      },
    })

    if (activeSubscription && activeSubscription.plan && activeSubscription.plan.restrictions.length > 0) {
      // Utiliser directement les types de la base de données sans conversion
      return activeSubscription.plan.restrictions.map((restriction) => ({
        type: restriction.type as PlanRestriction["type"],
        value: restriction.value,
      }))
    }
  } catch (error) {
    console.error("Error fetching user plan restrictions:", error)
  }

  //* Si pas d'abonnement actif, pas de restrictions dans le plan, ou erreur, retourner les restrictions par défaut
  return DEFAULT_PLAN_RESTRICTIONS
}

export async function getUserRestrictionValue(userId: string, type: PlanRestriction["type"]): Promise<number | null> {
  const restrictions = await getUserPlanRestrictions(userId)
  const restriction = restrictions.find((r) => r.type === type)

  if (restriction) {
    return restriction.value
  }

  // Trouver la valeur par défaut si la restriction spécifique n'est pas dans le plan de l'utilisateur
  const defaultRestriction = DEFAULT_PLAN_RESTRICTIONS.find((r) => r.type === type)
  return defaultRestriction ? defaultRestriction.value : null // Retourne null si même la valeur par défaut n'existe pas
}

export async function hasReachedAgentLimit(
  userId: string,
  currentAgentId?: number
): Promise<{
  hasReached: boolean
  limit: number | null
  hasConversationWithCurrentAgent: boolean
  uniqueAgentsCount: number
}> {
  const maxAgents = await getUserRestrictionValue(userId, "MAX_AGENTS")

  // Compter les agents uniques avec lesquels l'utilisateur a des conversations sauvegardées
  const uniqueAgents = await prisma.chat.findMany({
    where: {
      userId,
      isSaved: true,
    },
    select: {
      agentId: true,
    },
    distinct: ["agentId"],
  })

  // Vérifier si l'utilisateur a déjà une conversation avec l'agent actuel
  const hasConversationWithCurrentAgent = currentAgentId
    ? uniqueAgents.some((agent) => agent.agentId === currentAgentId)
    : false

  const uniqueAgentsCount = uniqueAgents.length

  // Si maxAgents est null, l'utilisateur a un accès illimité
  if (maxAgents === null) {
    return {
      hasReached: false,
      limit: null,
      hasConversationWithCurrentAgent,
      uniqueAgentsCount,
    }
  }

  // Si l'utilisateur a déjà une conversation avec l'agent actuel, il peut continuer à l'utiliser
  if (hasConversationWithCurrentAgent) {
    return {
      hasReached: false,
      limit: maxAgents,
      hasConversationWithCurrentAgent: true,
      uniqueAgentsCount,
    }
  }

  // Sinon, vérifier si l'utilisateur a atteint sa limite d'agents
  return {
    hasReached: uniqueAgentsCount >= maxAgents,
    limit: maxAgents,
    hasConversationWithCurrentAgent: false,
    uniqueAgentsCount,
  }
}

export async function getUserCategorySelections(userId: string): Promise<number[]> {
  try {
    const userSelection = await prisma.userCategorySelection.findUnique({
      where: { userId },
    })

    return userSelection?.categoryIds || []
  } catch (error) {
    console.error("Error fetching user category selections:", error)
    return []
  }
}

export async function canUserSelectMoreCategories(
  userId: string
): Promise<{ canSelect: boolean; limit: number | null; currentCount: number; remainingDays: number }> {
  const maxCategories = await getUserRestrictionValue(userId, "MAX_CATEGORIES")
  const currentSelections = await getUserCategorySelections(userId)

  // Vérifier si l'utilisateur a déjà fait une sélection et si la dernière mise à jour date de moins de 30 jours
  let canModifyDueToTimeRestriction = true

  let daysSinceLastUpdate = 0
  try {
    const existingSelection = await prisma.userCategorySelection.findUnique({
      where: { userId },
    })

    if (existingSelection) {
      const lastUpdate = new Date(existingSelection.lastUpdated)
      const now = new Date()
      daysSinceLastUpdate = Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24))

      // Vérifier si l'utilisateur a changé de plan récemment
      const userSubscriptions = await prisma.subscription.findMany({
        where: {
          userId,
          status: "ACTIVE",
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 2, // Nous avons besoin des deux derniers abonnements pour vérifier s'il y a eu un changement
      })

      // Vérifier si l'utilisateur a changé de plan récemment
      const hasPlanChanged = userSubscriptions.length > 1

      // Vérifier si l'utilisateur a besoin d'ajuster ses catégories en raison d'un changement de plan
      const needsAdjustmentDueToNewPlan =
        hasPlanChanged || (maxCategories !== null && existingSelection.categoryIds.length > maxCategories)

      // L'utilisateur peut modifier ses catégories si:
      // 1. 30 jours se sont écoulés depuis la dernière mise à jour
      // 2. L'utilisateur a changé de plan
      // 3. L'utilisateur a besoin d'ajuster ses catégories en raison d'un changement de plan
      canModifyDueToTimeRestriction = daysSinceLastUpdate >= DAYS_BEFORE_CATEGORY_CHANGE || needsAdjustmentDueToNewPlan
    }
  } catch (error) {
    console.error("Error checking user category selection time restriction:", error)
  }

  // L'utilisateur peut sélectionner plus de catégories si:
  // 1. Il a un plan avec des catégories illimitées (maxCategories === null)
  // 2. Il n'a pas atteint sa limite de catégories (currentSelections.length < maxCategories)
  // 3. Il respecte la restriction de temps ou a changé de plan (canModifyDueToTimeRestriction)
  const canSelectMoreCategories = maxCategories === null || currentSelections.length < maxCategories

  return {
    canSelect:
      (canSelectMoreCategories && canModifyDueToTimeRestriction) ||
      (maxCategories !== null && currentSelections.length > maxCategories), // Toujours permettre de réduire si trop de catégories
    limit: maxCategories,
    currentCount: currentSelections.length,
    remainingDays: canModifyDueToTimeRestriction ? 0 : DAYS_BEFORE_CATEGORY_CHANGE - daysSinceLastUpdate,
  }
}
