export function getBrowserInfo(): {
  AcceptHeader: string
  JavaEnabled: boolean
  JavascriptEnabled: boolean
  Language: string
  ColorDepth: number
  ScreenHeight: number
  ScreenWidth: number
  TimeZoneOffset: number
  UserAgent: string
} {
  return {
    AcceptHeader: navigator.userAgent.includes("Safari")
      ? "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
      : "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    JavaEnabled: false,
    JavascriptEnabled: true,
    Language: navigator.language || "en-US",
    ColorDepth: screen.colorDepth,
    ScreenHeight: screen.height,
    ScreenWidth: screen.width,
    TimeZoneOffset: new Date().getTimezoneOffset() * -1,
    UserAgent: navigator.userAgent,
  }
}

export type BrowserInfoType = ReturnType<typeof getBrowserInfo>
