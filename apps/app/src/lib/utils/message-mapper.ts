// utils/message-mapper.ts
import { Message as AIMessage } from "ai"

// Create a simple message structure that doesn't depend on specific library versions
export interface BasicMessage {
  id: string
  role: "user" | "assistant" | "system" | "data"
  content: string
  createdAt?: Date
}

/**
 * Converts messages from any source to a basic format suitable for storage
 */
export function toBasicMessages(messages: AIMessage[]): BasicMessage[] {
  return messages.map((msg) => ({
    id: msg.id || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    role: msg.role,
    content: msg.content,
    createdAt: msg.createdAt || new Date(),
  }))
}

/**
 * Safely converts basic messages to AI SDK format
 * This uses a simple structure that matches what the AI SDK expects
 * without relying on specific type definitions
 */
export function toAIMessages(messages: BasicMessage[]): AIMessage[] {
  return messages.map((msg) => ({
    id: msg.id,
    role: msg.role,
    content: msg.content,
    createdAt: msg.createdAt,
  }))
}

/**
 * Logs message details for debugging
 */
// export function logMessages(prefix: string, messages: AIMessage[]): void {
//   console.log(`${prefix}: ${messages.length} messages`)
//   if (messages.length > 0) {
//     console.log(`First message: ${messages[0].role} - ${messages[0].content.substring(0, 30)}...`)
//     console.log(
//       `Last message: ${messages[messages.length - 1].role} - ${messages[messages.length - 1].content.substring(0, 30)}...`
//     )
//   }
// }
