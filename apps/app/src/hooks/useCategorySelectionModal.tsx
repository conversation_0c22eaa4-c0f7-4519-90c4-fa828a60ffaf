"use client"

import { useCallback, useRef, useState } from "react"

import CategorySelectionModal from "@/components/profile/CategorySelectionModal"

interface UseCategorySelectionModalOptions {
  onSuccess?: () => void
  forceSelection?: boolean
  title?: string
  description?: string
}

export const useCategorySelectionModal = (options: UseCategorySelectionModalOptions = {}) => {
  // Utiliser useRef pour suivre l'état du modal sans déclencher de re-rendus
  const isOpenRef = useRef(false)
  const [isOpen, setIsOpen] = useState(false)

  // Utiliser useCallback pour éviter les re-rendus inutiles
  const openModal = useCallback(() => {
    // Ne pas ouvrir si déjà ouvert
    if (!isOpenRef.current) {
      isOpenRef.current = true
      setIsOpen(true)
    }
  }, [])

  const closeModal = useCallback(() => {
    // Fermer le modal dans tous les cas
    isOpenRef.current = false
    setIsOpen(false)
  }, [])

  // Mémoriser le composant pour éviter les re-rendus inutiles
  const CategorySelectionModalComponent = useCallback(
    () => (
      <CategorySelectionModal
        isOpen={isOpen}
        onClose={closeModal}
        onSuccess={options.onSuccess}
        forceSelection={options.forceSelection}
        title={options.title}
        description={options.description}
      />
    ),
    [isOpen, closeModal, options.onSuccess, options.forceSelection, options.title, options.description]
  )

  return {
    openCategorySelectionModal: openModal,
    closeCategorySelectionModal: closeModal,
    CategorySelectionModal: CategorySelectionModalComponent,
  }
}
