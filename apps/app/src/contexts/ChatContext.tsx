"use client"

import Re<PERSON>, { create<PERSON>ontext, ReactNode, useC<PERSON>back, useContext, useEffect, useRef, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { Message as AIMessage } from "ai"

import { canSaveMoreChats, createChat, deleteChat as deleteServerChat, saveChat } from "@/actions/chat-actions"
import { getUserRestrictionValue, hasReachedAgentLimit } from "@/lib/plan"
import { logger } from "@coheadcoaching/lib"

type ChatContextType = {
  chatId: string | null
  setChatId: (id: string | null) => void
  isSaved: boolean
  saveCurrentChat: (messages: AIMessage[]) => Promise<void>
  deleteChat: (id: string) => Promise<void>
  resetChat: () => void
  setIsSaved: React.Dispatch<React.SetStateAction<boolean>>
  canSave: boolean
  saveLimit: number | null
  messageLimit: number | null
  agentLimitReached: boolean
  hasConversationWithCurrentAgent: boolean
  agentLimit: number | null
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function ChatProvider({ children, agentId }: { children: ReactNode; agentId: number }) {
  const [chatId, setChatId] = useState<string | null>(null)
  const [isSaved, setIsSaved] = useState(false)
  const isInitializedRef = useRef(false)
  const skipFetchRef = useRef(false)

  const { data: session } = useSession()
  const [canSave, setCanSave] = useState(true)
  const [saveLimit, setSaveLimit] = useState<number | null>(null)
  const [messageLimit, setMessageLimit] = useState<number | null>(null)
  const [agentLimitReached, setAgentLimitReached] = useState(false)
  const [hasConversationWithCurrentAgent, setHasConversationWithCurrentAgent] = useState(false)
  const [agentLimit, setAgentLimit] = useState<number | null>(null)

  // Vérifier les limites du plan
  const checkLimits = useCallback(
    async (userId: string) => {
      if (!userId) return
      try {
        // Vérifier la limite de sauvegarde
        const { canSave, limit } = await canSaveMoreChats()
        setCanSave(canSave)
        setSaveLimit(limit)

        // Vérifier la limite de messages par chat

        const maxMessages = await getUserRestrictionValue(userId, "MAX_MESSAGES_PER_CHAT")
        setMessageLimit(maxMessages)

        // Vérifier la limite d'agents
        const {
          hasReached,
          limit: agentLimitValue,
          hasConversationWithCurrentAgent: hasConvo,
        } = await hasReachedAgentLimit(userId, agentId)

        setAgentLimitReached(hasReached && !hasConvo)
        setHasConversationWithCurrentAgent(hasConvo)
        setAgentLimit(agentLimitValue)
      } catch (error) {
        logger.error("Error checking limits:", error)
      }
    },
    [agentId]
  )

  useEffect(() => {
    checkLimits(session?.user?.id)
  }, [checkLimits, session?.user?.id, chatId])

  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize chatId from URL parameter on mount
  useEffect(() => {
    if (!isInitializedRef.current) {
      const urlChatId = searchParams.get("chatId")
      if (urlChatId) {
        setChatId(urlChatId)
      }
      isInitializedRef.current = true
    }
  }, [searchParams])

  // Update URL when chatId changes
  const updateUrl = useCallback(
    (newChatId: string | null) => {
      const params = new URLSearchParams(searchParams.toString())

      if (newChatId) {
        params.set("chatId", newChatId)
      } else {
        params.delete("chatId")
      }

      router.push(`?${params.toString()}`, { scroll: false })
    },
    [router, searchParams]
  )

  // Enhanced setChatId that also updates URL
  const setChatIdWithUrl = useCallback(
    (id: string | null, skipFetch = false) => {
      if (skipFetch) {
        skipFetchRef.current = true
      }
      setChatId(id)
      updateUrl(id)
    },
    [updateUrl]
  )

  const saveCurrentChat = async (messages: AIMessage[]): Promise<void> => {
    try {
      if (!chatId) {
        // Create a new chat on the server
        const newChatId = await createChat(agentId)

        // Save the messages to the new chat
        await saveChat(newChatId, messages, true)

        // Update the chat ID after saving but skip fetch
        setChatIdWithUrl(newChatId, true)
      } else {
        // Save messages to existing chat
        await saveChat(chatId, messages, true)
      }

      setIsSaved(true)
    } catch (error) {
      logger.error("Error saving chat:", error)
    }
  }

  const resetChat = (): void => {
    setChatIdWithUrl(null)
    setIsSaved(false)
  }

  const deleteChat = async (id: string): Promise<void> => {
    try {
      await deleteServerChat(id)

      // If we deleted the current chat, reset state
      if (id === chatId) {
        resetChat()
      }
      checkLimits(session?.user?.id)
    } catch (error) {
      logger.error("Error deleting chat:", error)
    }
  }

  return (
    <ChatContext.Provider
      value={{
        chatId,
        setChatId: setChatIdWithUrl,
        isSaved,
        saveCurrentChat,
        deleteChat,
        resetChat,
        setIsSaved,
        canSave,
        saveLimit,
        messageLimit,
        agentLimitReached,
        hasConversationWithCurrentAgent,
        agentLimit,
      }}
    >
      {children}
    </ChatContext.Provider>
  )
}

export function useChatContext(): ChatContextType {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error("useChatContext must be used within a ChatProvider")
  }
  return context
}
