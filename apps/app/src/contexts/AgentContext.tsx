"use client"
import { createContext, useContext } from "react"

import { Agent } from "@prisma/client"

const AgentContext = createContext<Agent | null>(null)

export const AgentProvider = ({ agent, children }: { agent: Agent; children: React.ReactNode }) => {
  return <AgentContext.Provider value={agent}>{children}</AgentContext.Provider>
}

export const useAgent = () => {
  const context = useContext(AgentContext)
  if (!context) {
    throw new Error("useAgent must be used within an AgentProvider")
  }
  return context
}
