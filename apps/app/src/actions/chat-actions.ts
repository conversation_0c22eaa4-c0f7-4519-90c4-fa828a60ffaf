// app/actions/chat-actions.ts
"use server"

import { revalidatePath } from "next/cache"
import { generateText } from "ai"

import { auth } from "@/lib/auth"
import { getUserRestrictionValue } from "@/lib/plan"
import { prisma } from "@/lib/prisma"
import { BasicMessage, toBasicMessages } from "@/lib/utils/message-mapper"
import { SavedChat } from "@/types/chat"
import { google } from "@ai-sdk/google"
import { openai } from "@ai-sdk/openai"
import { logger } from "@coheadcoaching/lib"

// Create a new chat
export async function createChat(agentId: number): Promise<string> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  const { canSave } = await canSaveMoreChats()

  if (!canSave) {
    throw new Error("Vous avez atteint la limite de chats sauvegardés pour votre plan actuel.")
  }

  const chat = await prisma.chat.create({
    data: {
      userId: session.user.id,
      agentId,
    },
  })

  return chat.id
}

async function generateChatTitle(messages: BasicMessage[]): Promise<string | null> {
  if (messages.length === 0) return null

  const contextMessages = messages.slice(1, 4)
  const messagesText = contextMessages.map((msg) => `${msg.role}: ${msg.content.substring(0, 200)}`).join("\n")
  logger.log(messagesText)
  const titleGenerationPrompt = `Génère un titre court et pertinent (maximum 50 caractères, sans aucun formatage) pour cette conversation, basé sur ces messages:\n${messagesText}\n\nTitre:`

  try {
    const { text } = await generateText({
      model: google("gemini-2.0-flash-001"),
      prompt: titleGenerationPrompt,
      maxTokens: 20,
    })

    return text.trim().substring(0, 50)
  } catch {
    try {
      const { text } = await generateText({
        model: openai("gpt-3.5-turbo"),
        prompt: titleGenerationPrompt,
        maxTokens: 20,
      })

      return text.trim().substring(0, 50)
    } catch (error) {
      logger.error("Erreur lors de la génération du titre:", error)

      const firstUserMsg = messages.find((m) => m.role === "user")
      if (firstUserMsg) {
        let title = firstUserMsg.content.substring(0, 50)
        if (firstUserMsg.content.length > 50) title += "..."
        return title
      }
    }

    return null
  }
}

// Save chat messages
export async function saveChat(chatId: string, messages: BasicMessage[], updateSaved: boolean = false): Promise<void> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  // Verify this chat belongs to the current user
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    select: { userId: true, agentId: true },
  })

  if (!chat || chat.userId !== session.user.id) {
    throw new Error("Unauthorized access to chat")
  }

  // Vérifier la restriction du nombre de messages par chat si on sauvegarde
  if (updateSaved) {
    const maxMessagesPerChat = await getUserRestrictionValue(session.user.id, "MAX_MESSAGES_PER_CHAT")
    if (maxMessagesPerChat !== null && messages.length > maxMessagesPerChat) {
      throw new Error(
        `Vous avez dépassé la limite de ${maxMessagesPerChat} messages par conversation pour votre plan actuel.`
      )
    }
  }

  // Convert messages to a consistent format
  const basicMessages = toBasicMessages(messages)

  // Delete existing messages for this chat
  await prisma.message.deleteMany({
    where: { chatId },
  })

  // Create new messages
  for (const msg of basicMessages) {
    await prisma.message.create({
      data: {
        id: msg.id,
        role: msg.role,
        content: msg.content,
        chatId,
      },
    })
  }

  // Générer le titre avec l'IA
  const title = await generateChatTitle(basicMessages)

  // Update chat
  await prisma.chat.update({
    where: { id: chatId },
    data: {
      title,
      isSaved: updateSaved,
      updatedAt: new Date(),
    },
  })
}

// Get user's saved chats for a specific agent
export async function getUserSavedChats(agentId: number): Promise<SavedChat[]> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  const chats = await prisma.chat.findMany({
    where: {
      userId: session.user.id,
      agentId,
      isSaved: true,
    },
    orderBy: {
      updatedAt: "desc",
    },
    select: { id: true, title: true, updatedAt: true },
  })

  return chats
}

// Get all user's saved chats
export async function getAllUserSavedChats(
  limit: number = 5
): Promise<Array<{ id: string; title: string | null; agentName?: string }>> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  const chats = await prisma.chat.findMany({
    where: {
      userId: session.user.id,
      isSaved: true,
    },
    orderBy: {
      updatedAt: "desc",
    },
    select: {
      id: true,
      title: true,
      agent: {
        select: {
          title: true,
        },
      },
    },
    take: limit,
  })

  return chats.map((chat) => ({
    id: chat.id,
    title: chat.title,
    agentName: chat.agent?.title,
  }))
}

// Check if user can save more chats
export async function canSaveMoreChats(): Promise<{ canSave: boolean; limit: number | null; savedChatsCount: number }> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  const maxSavedChats = await getUserRestrictionValue(session.user.id, "MAX_SAVED_CHATS")

  const savedChatsCount = await prisma.chat.count({
    where: {
      userId: session.user.id,
      isSaved: true,
    },
  })

  return {
    canSave: maxSavedChats === null || savedChatsCount < maxSavedChats,
    limit: maxSavedChats,
    savedChatsCount,
  }
}

// Delete a chat
export async function deleteChat(chatId: string): Promise<void> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error("User not authenticated")
  }

  // Verify this chat belongs to the current user
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    select: { userId: true, agentId: true },
  })

  if (!chat || chat.userId !== session.user.id) {
    throw new Error("Unauthorized access to chat")
  }

  // Delete all messages first to ensure clean deletion
  await prisma.message.deleteMany({
    where: { chatId },
  })

  // Delete the chat
  await prisma.chat.delete({
    where: { id: chatId },
  })

  revalidatePath("/agent/[idAgent]")
}
