"use server"

import { auth } from "@/lib/auth"
import { createMangopayUser as createMangopayUserLib, registerCard as registerCardLib } from "@/lib/mangopay"
import { logger } from "@coheadcoaching/lib"

interface UserData {
  firstName: string
  lastName: string
  email: string
  address: {
    addressLine1: string
    addressLine2?: string | null
    city: string
    postalCode: string
    country: string
    region?: string
  }
  nationality?: string
  countryOfResidence?: string
  birthday?: number
}

interface CardData {
  cardNumber: string
  cardExpirationDate: string // Format: MMYY
  cardCvx: string
  cardType: "CB_VISA_MASTERCARD" | "AMEX" | "MAESTRO" | "BCMC"
}

export async function createMangopayUser(userData: UserData) {
  const session = await auth()

  if (!session?.user?.id) {
    throw new Error("Vous devez être connecté pour effectuer cette action")
  }

  try {
    return await createMangopayUserLib(
      session.user.id,
      {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        address: {
          addressLine1: userData.address.addressLine1,
          addressLine2: userData.address.addressLine2,
          city: userData.address.city,
          region: userData.address.region,
          postalCode: userData.address.postalCode,
          country: userData.address.country,
        },
        birthday: userData.birthday,
        nationality: userData.nationality || userData.address.country,
        countryOfResidence: userData.countryOfResidence || userData.address.country,
      },
      "PAYER"
    )
  } catch (error) {
    logger.error("Error creating MangoPay user:", error)
    throw new Error(error instanceof Error ? error.message : "Erreur lors de la création du compte MangoPay")
  }
}

export async function registerCard(cardData: CardData) {
  const session = await auth()

  if (!session?.user?.id) {
    throw new Error("Vous devez être connecté pour effectuer cette action")
  }

  try {
    return await registerCardLib(session.user.id, {
      cardNumber: cardData.cardNumber,
      cardExpirationDate: cardData.cardExpirationDate,
      cardCvx: cardData.cardCvx,
      cardType: cardData.cardType,
    })
  } catch (error) {
    logger.error("Error registering card:", error)
    throw new Error(error instanceof Error ? error.message : "Erreur lors de l'enregistrement de la carte")
  }
}
