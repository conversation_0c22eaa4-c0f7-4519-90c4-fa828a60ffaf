export enum TicketStatus {
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
}

export interface SupportTicket {
  id: string
  title: string
  status: TicketStatus
  userId: string
  user: {
    id: string
    name?: string | null
    email?: string | null
  }
  messages: SupportMessage[]
  createdAt: Date
  updatedAt: Date
}

export interface SupportMessage {
  id: string
  content: string
  ticketId: string
  senderId: string
  sender: {
    id: string
    name?: string | null
    role: string
  }
  attachmentId?: string | null
  attachment?: File | null
  createdAt: Date
}
