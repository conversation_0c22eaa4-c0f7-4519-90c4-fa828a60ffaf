type UserCategory = "OWNER" | "PAYER"

type Address = {
  AddressLine1: string
  AddressLine2?: string
  City: string
  Region?: string
  PostalCode: string
  Country: string
}

type CurrencyAmount = {
  Currency: string
  Amount: number
}

type CurrentState = {
  PayinsLinked: number
  CumulatedDebitedAmount: CurrencyAmount
  CumulatedFeesAmount: CurrencyAmount
  LastPayinId: string | null
}

type Contact = {
  FirstName: string
  LastName: string
  Address: Address
}

type BrowserInfo = {
  AcceptHeader: string
  JavaEnabled?: boolean
  JavascriptEnabled?: boolean
  Language: string
  ColorDepth: number
  ScreenHeight: number
  ScreenWidth: number
  TimeZoneOffset: number
  UserAgent: string
  IpAddress: string
}

type CardInfo = {
  BIN: string
  IssuingBank: string
  IssuerCountryCode: string
  Type: "DEBIT" | "CREDIT" | "CHARGE CARD"
  Brand: string
  SubType: string
}

export type CardType = "CB_VISA_MASTERCARD" | "AMEX" | "MAESTRO" | "BCMC"

type SecurityInfo = { AVSResult: string }

export type MangopayUser = {
  Address: Address
  FirstName: string
  LastName: string
  Birthday: number
  Nationality: string
  CountryOfResidence: string
  Occupation: string | null
  IncomeRange: string | null
  ProofOfIdentity: string | null
  ProofOfAddress: string | null
  Capacity: string // "NORMAL"
  PhoneNumber: string | null
  PhoneNumberCountry: string | null
  Id: string
  Tag: string
  PersonType: "NATURAL" | "LEGAL"
  Email: string
  KYCLevel: string // "LIGHT"
  TermsAndConditionsAccepted: boolean
  TermsAndConditionsAcceptedDate: number
  UserCategory: UserCategory
  UserStatus: string // "ACTIVE"
}

type Balance = {
  Currency: string
  Amount: number
}

export type MangopayWallet = {
  Description: string
  Owners: string[]
  Id: string
  Balance: Balance
  Currency: string
  FundsType: string // "DEFAULT";
  Tag: string
  CreationDate: number
}

export type MangopayCardRegistration = {
  Id: string
  Tag: string | null
  CreationDate: number
  UserId: string
  AccessKey: string
  PreregistrationData: string
  RegistrationData: string | null
  CardId: string | null
  CardType: CardType
  CardRegistrationURL: string
  ResultCode: string | null
  ResultMessage: string | null
  Currency: string
  Status: "CREATED" | "VALIDATED" | "ERROR"
}

export type MangopayErrorResponse = {
  Message: string
  Type: string
  Id: string
  Date: number
  errors: Record<string, string> | null
}

export type MangopayRecurringPayinRegistration = {
  Id: string
  Status: "CREATED" | "AUTHENTICATION_NEEDED" | "IN_PROGRESS" | "ENDED"
  ResultCode: string | null
  ResultMessage: string | null
  CurrentState: CurrentState
  RecurringType: "CLASSIC_SUBSCRIPTION" | "FRACTIONED_PAYMENT" | "CUSTOM"
  TotalAmount: CurrencyAmount | null
  CycleNumber: number | null
  AuthorId: string
  CardId: string
  CreditedUserId: string
  CreditedWalletId: string
  Billing: Contact
  Shipping: Contact
  EndDate: number | null // timestamp in seconds
  Frequency:
    | "Daily"
    | "Weekly"
    | "TwiceAMonth"
    | "Monthly"
    | "Bimonthly"
    | "Quarterly"
    | "Semiannual"
    | "Annual"
    | "Biannual"
  FixedNextAmount: boolean
  FractionedPayment: boolean
  FreeCycles: number
  FirstTransactionDebitedFunds: CurrencyAmount
  FirstTransactionFees: CurrencyAmount
  NextTransactionDebitedFunds: CurrencyAmount | null
  NextTransactionFees: CurrencyAmount | null
  Migration: false // deprecated, always false
  PaymentType: "CARD_DIRECT" | "PAYPAL"
}

export type MangopayRecurringPayIn = {
  Id: string
  Tag?: string
  CreationDate: number // timestamp
  AuthorId: string
  CreditedUserId: string
  DebitedFunds: CurrencyAmount
  CreditedFunds: CurrencyAmount
  Fees: CurrencyAmount
  Status: "CREATED" | "SUCCEEDED" | "FAILED"
  ResultCode: string
  ResultMessage: string
  ExecutionDate: number | null
  Type: "PAYIN" | "TRANSFER" | "CONVERSION" | "PAYOUT"
  Nature: "REGULAR" | "REPUDIATION" | "REFUND" | "SETTLEMENT"
  CreditedWalletId: string
  DebitedWalletId: string | null
  PaymentType: "CARD" | "DIRECT_DEBIT" | "PREAUTHORIZED" | "BANK_WIRE"
  ExecutionType: "WEB" | "DIRECT" | "EXTERNAL_INSTRUCTION"
  SecureMode: "DEFAULT" | "FORCE" | "NO_CHOICE"
  CardId: string
  SecureModeReturnURL?: string
  SecureModeRedirectURL?: string
  SecureModeNeeded: boolean
  Culture?: string
  SecurityInfo?: SecurityInfo
  StatementDescriptor?: string
  BrowserInfo?: BrowserInfo
  Billing?: Contact
  Shipping?: Contact
  Requested3DSVersion?: "V1" | "V2_1"
  Applied3DSVersion?: "V1" | "V2_1"
  RecurringPayinRegistrationId: string
  PreferredCardNetwork?: "VISA" | "MASTERCARD" | "CB" | "MAESTRO"
  CardInfo?: CardInfo | null
}

export type MangopayWebhookBody = {
  EventType: string
  ResourceId: string
}

type CardProvider = "CB" | "VISA" | "MASTERCARD" | "AMEX" | "MAESTRO" | "BCMC" | "JCB" | "DISCOVER"

type Validity = "UNKNOWN" | "VALID" | "INVALID"

export type MangopayCardType = {
  Id: string
  UserId: string
  Alias: string
  ExpirationDate: string
  CardType: CardType
  CardProvider: CardProvider
  Country: string
  Product: string
  BankCode: string
  Active: boolean
  Currency: string
  Validity: Validity
  CreationDate: number
  Fingerprint: string
  CardHolderName: string
  Tag?: string
}

type Money = {
  Amount: number
  Currency: string
}

type TransactionStatus = "CREATED" | "SUCCEEDED" | "FAILED"

type TransactionType = "PAYIN" | "TRANSFER" | "CONVERSION" | "PAYOUT"

type TransactionNature = "REGULAR" | "REPUDIATION" | "REFUND" | "SETTLEMENT"

type RefundReasonType =
  | "INITIALIZED_BY_CLIENT"
  | "BANKACCOUNT_INCORRECT"
  | "OWNER_DO_NOT_MATCH_BANKACCOUNT"
  | "BANKACCOUNT_HAS_BEEN_CLOSED"
  | "WITHDRAWAL_IMPOSSIBLE_ON_SAVINGS_ACCOUNTS"
  | "OTHER"

type RefundReason = {
  RefundReasonMessage?: string
  RefundReasonType?: RefundReasonType
}

export type MangopayRefund = {
  Id: string
  Tag?: string
  CreationDate: number
  AuthorId: string
  CreditedUserId?: string
  DebitedFunds?: Money
  CreditedFunds?: Money
  Fees?: Money
  Status: TransactionStatus
  ResultCode?: string
  ResultMessage?: string
  ExecutionDate: number | null
  Type: TransactionType
  Nature: TransactionNature
  InitialTransactionId: string
  InitialTransactionType: "PAYIN" | "TRANSFER" | "PAYOUT"
  InitialTransactionNature: TransactionNature
  DebitedWalletId: string
  CreditedWalletId: string
  RefundReason?: RefundReason
}
