// Quick test script to create and activate a cancellation form
const { PrismaClient } = require("@prisma/client")

const prisma = new PrismaClient()

async function createTestForm() {
  try {
    // Get the first user from the database
    const firstUser = await prisma.user.findFirst()
    if (!firstUser) {
      console.error("❌ No users found in database. Please create a user first.")
      return
    }

    console.log("📝 Using user:", firstUser.email)

    // Check if there's already an active form
    const existingActiveForm = await prisma.form.findFirst({
      where: {
        type: "SUBSCRIPTION_CANCELLATION",
        isActive: true,
      },
    })

    if (existingActiveForm) {
      console.log("✅ Active form already exists:", existingActiveForm.title)
      console.log("Form ID:", existingActiveForm.id)

      // Verify it has questions
      const questions = await prisma.formQuestion.findMany({
        where: { formId: existingActiveForm.id },
        orderBy: { order: "asc" },
      })

      console.log("✅ Questions count:", questions.length)
      return
    }

    // First, deactivate any existing forms (shouldn't be any due to unique constraint)
    await prisma.form.updateMany({
      where: {
        type: "SUBSCRIPTION_CANCELLATION",
        isActive: true,
      },
      data: {
        isActive: false,
        status: "INACTIVE",
      },
    })

    // Create a new test form
    const form = await prisma.form.create({
      data: {
        title: "Formulaire d'annulation d'abonnement",
        description: "Aidez-nous à comprendre pourquoi vous annulez votre abonnement.",
        type: "SUBSCRIPTION_CANCELLATION",
        status: "ACTIVE",
        isActive: true,
        showProgressBar: true,
        allowSaveProgress: false,
        requireAuth: true,
        enableConditionalLogic: false,
        createdBy: firstUser.id,
      },
    })

    // Add test questions
    const questions = [
      {
        title: "Quelle est la principale raison de votre annulation ?",
        description: "Sélectionnez la raison qui correspond le mieux à votre situation.",
        type: "SINGLE_CHOICE",
        order: 0,
        isRequired: true,
        formId: form.id,
        options: JSON.stringify([
          { id: "price", label: "Prix trop élevé", value: "price" },
          { id: "features", label: "Fonctionnalités insuffisantes", value: "features" },
          { id: "support", label: "Support client insatisfaisant", value: "support" },
          { id: "competitor", label: "Trouvé une meilleure alternative", value: "competitor" },
          { id: "other", label: "Autre raison", value: "other" },
        ]),
      },
      {
        title: "Évaluez votre satisfaction globale",
        description: "Sur une échelle de 1 à 5, comment évaluez-vous votre expérience ?",
        type: "RATING",
        order: 1,
        isRequired: true,
        formId: form.id,
        minValue: 1,
        maxValue: 5,
      },
      {
        title: "Recommanderiez-vous notre service à un ami ?",
        type: "YES_NO",
        order: 2,
        isRequired: true,
        formId: form.id,
      },
      {
        title: "Commentaires additionnels",
        description: "Partagez vos commentaires pour nous aider à améliorer notre service.",
        type: "TEXT_LONG",
        order: 3,
        isRequired: false,
        formId: form.id,
        maxLength: 500,
      },
    ]

    await prisma.formQuestion.createMany({
      data: questions,
    })

    console.log("✅ Test form created and activated successfully!")
    console.log("Form ID:", form.id)
    console.log("Questions created:", questions.length)

    // Verify the form is active
    const activeForm = await prisma.form.findFirst({
      where: {
        type: "SUBSCRIPTION_CANCELLATION",
        status: "ACTIVE",
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: "asc" },
        },
      },
    })

    if (activeForm) {
      console.log("✅ Active form verified:", activeForm.title)
      console.log("✅ Questions count:", activeForm.questions.length)
    } else {
      console.log("❌ No active form found")
    }
  } catch (error) {
    console.error("❌ Error creating test form:", error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestForm()
