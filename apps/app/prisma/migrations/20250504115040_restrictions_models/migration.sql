-- Create<PERSON><PERSON>
CREATE TYPE "RestrictionType" AS ENUM ('MAX_MESSAGES_PER_CHAT', 'MAX_SAVED_CHATS', 'MAX_AGENTS', 'MAX_CATEGORIES');

-- CreateTable
CREATE TABLE "PlanRestriction" (
    "id" TEXT NOT NULL,
    "type" "RestrictionType" NOT NULL,
    "value" INTEGER,
    "planId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PlanRestriction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserCategorySelection" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "categoryIds" INTEGER[],
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserCategorySelection_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserCategorySelection_userId_key" ON "UserCategorySelection"("userId");

-- AddForeignKey
ALTER TABLE "PlanRestriction" ADD CONSTRAINT "PlanRestriction_planId_fkey" FOREIGN KEY ("planId") REFERENCES "Plan"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserCategorySelection" ADD CONSTRAINT "UserCategorySelection_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
