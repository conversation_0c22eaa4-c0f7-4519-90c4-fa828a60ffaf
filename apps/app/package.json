{"name": "@coheadcoaching/app", "license": "MIT", "scripts": {"dev": "npm run is-initialized && prisma migrate dev && cross-env FORCE_COLOR=1 next dev", "build": "next build", "type-check": "tsc --noEmit", "start": "npm run deploy-db:prod && next start --port ${PORT:-3000}", "deploy-db:prod": "prisma db push --accept-data-loss && prisma migrate deploy && npm run seed", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --check \"**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"**/*.{js,jsx,ts,tsx}\"", "test": "cross-env FORCE_COLOR=1 jest --passWithNoTests", "seed": "cross-env NODE_ENV=development tsx prisma/seed.ts", "is-initialized": "cd ../../packages/scripts && npm run is-initialized", "analyze": "ANALYZE=true npm run build && cd .next/analyze && npx -y serve", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/google": "^1.1.17", "@ai-sdk/openai": "^1.1.13", "@ai-sdk/react": "^1.1.18", "@auth/prisma-adapter": "^2.2.0", "@aws-sdk/client-s3": "^3.454.0", "@aws-sdk/s3-presigned-post": "^3.454.0", "@coheadcoaching/lib": "*", "@coheadcoaching/scripts": "*", "@coheadcoaching/transactional": "*", "@coheadcoaching/tsconfig": "*", "@formatjs/intl-localematcher": "^0.5.0", "@hookform/resolvers": "^3.2.0", "@next/bundle-analyzer": "^14.1.2", "@nextui-org/accordion": "^2.2.7", "@nextui-org/avatar": "^2.0.30", "@nextui-org/breadcrumbs": "^2.2.6", "@nextui-org/button": "^2.0.34", "@nextui-org/card": "^2.0.31", "@nextui-org/checkbox": "^2.1.2", "@nextui-org/chip": "^2.2.6", "@nextui-org/date-picker": "^2.3.9", "@nextui-org/dropdown": "^2.3.9", "@nextui-org/image": "^2.2.5", "@nextui-org/input": "^2.2.2", "@nextui-org/link": "^2.0.32", "@nextui-org/modal": "^2.0.36", "@nextui-org/navbar": "^2.2.8", "@nextui-org/pagination": "^2.2.8", "@nextui-org/progress": "^2.2.6", "@nextui-org/radio": "^2.3.8", "@nextui-org/select": "^2.2.2", "@nextui-org/skeleton": "^2.0.29", "@nextui-org/slider": "^2.4.7", "@nextui-org/spinner": "^2.0.30", "@nextui-org/switch": "^2.0.31", "@nextui-org/system": "^2.4.6", "@nextui-org/table": "^2.2.8", "@nextui-org/tabs": "^2.2.7", "@nextui-org/theme": "^2.4.5", "@nextui-org/tooltip": "^2.0.36", "@nextui-org/user": "^2.2.6", "@prisma/client": "^5.12.1", "@react-email/render": "^0.0.15", "@t3-oss/env-nextjs": "^0.10.0", "@tanstack/react-query": "^5.39.0", "@tanstack/react-query-devtools": "^5.39.0", "@total-typescript/ts-reset": "^0.5.0", "@trpc/client": "^11.0.0-rc.377", "@trpc/react-query": "^11.0.0-rc.377", "@trpc/server": "^11.0.0-rc.377", "@types/bcryptjs": "^2.4.2", "@types/crypto-js": "^4.2.1", "@types/negotiator": "^0.6.3", "@types/nodemailer": "^6.4.14", "@types/react-dom": "^18.2.7", "@types/react-payment-inputs": "^1.1.4", "@types/request-ip": "^0.0.41", "@types/ua-parser-js": "^0.7.39", "ai": "^4.1.45", "autoprefixer": "^10.4.16", "base32-encode": "^2.0.0", "bcryptjs": "^2.4.3", "bip39": "^3.1.0", "clsx": "^2.0.0", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "custom-prettier-config": "*", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "eslint-config-custom": "*", "framer-motion": "^11.18.2", "ioredis": "^5.3.2", "lucide-react": "^0.395.0", "negotiator": "^0.6.3", "next": "^14.2.4", "next-auth": "5.0.0-beta.19", "next-themes": "^0.3.0", "nextjs-toploader": "^3.8.16", "nodemailer": "^6.9.5", "ora": "^8.0.1", "otpauth": "^9.2.2", "prisma": "^5.12.1", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-country-region-selector": "^4.0.2", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-payment-inputs": "^1.2.0", "react-select": "^5.10.0", "react-toastify": "^10.0.0", "request-ip": "^3.3.0", "sharp": "^0.33.0", "sonner": "^2.0.3", "superjson": "^2.0.0", "tailwind-merge": "^2.0.0", "tsx": "^4.7.0", "ua-parser-js": "^1.0.35", "xlsx": "^0.18.5", "zod": "^3.21.4"}, "devDependencies": {"@babel/core": "^7.22.17", "@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^16.0.0", "@types/jest": "^29.5.5", "@types/node": "^20.4.9", "@types/react": "^18.2.19", "babel-plugin-styled-components": "^2.1.4", "fetch-mock": "^10.0.0", "git-conventional-commits": "^2.6.5", "isomorphic-fetch": "^3.0.0", "jest": "^29.6.2", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.29", "postinstall-postinstall": "^2.1.0", "tailwindcss": "^3.3.3", "ts-jest": "^29.1.1", "typescript": "^5.3.2", "vitest": "^1.0.0"}, "engines": {"node": ">=18.15.0"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "npx -y tsx prisma/seed.ts"}}