import * as path from "path"

import { nextui } from "@nextui-org/theme"

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    ...["pages/**/*.{ts,tsx}", "components/**/*.{ts,tsx}", "app/**/*.{ts,tsx}", "src/**/*.{ts,tsx}"].map((p) =>
      path.join(__dirname, p)
    ),
    path.join(__dirname, "../..", "node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}"),
  ],
  theme: {
    extend: {
      colors: {
        muted: "hsl(var(--nextui-default-100))",
        "muted-foreground": "hsl(var(--nextui-default-500))",
      },
    },
  },
  darkMode: "class",
  plugins: [
    nextui({
      themes: {
        light: {
          colors: {
            primary: {
              DEFAULT: "rgb(253, 108, 52)", // Couleur principale
              50: "rgb(255, 248, 244)", // Teinte la plus claire
              100: "rgb(255, 240, 234)",
              200: "rgb(255, 167, 133)",
              300: "rgb(255, 154, 107)",
              400: "rgb(255, 140, 85)",
              500: "rgb(253, 108, 52)", // DEFAULT
              600: "rgb(247, 77, 55)",
              700: "rgb(230, 67, 30)",
              800: "rgb(207, 58, 0)",
              900: "rgb(150, 42, 0)",
            },
            default: {
              DEFAULT: "rgb(148, 148, 148)", // Texte / bordure principale
              50: "rgb(245, 245, 245)", // Très clair
              100: "rgb(235, 235, 235)",
              200: "rgb(211, 211, 211)",
              300: "rgb(180, 180, 180)",
              400: "rgb(165, 165, 165)",
              500: "rgb(148, 148, 148)", // DEFAULT
              600: "rgb(120, 120, 120)",
              700: "rgb(104, 104, 104)",
              800: "rgb(80, 80, 80)",
              900: "rgb(50, 50, 50)",
            },
          },
        },
        dark: {
          colors: {
            primary: {
              DEFAULT: "rgb(255, 154, 107)",
              50: "rgb(40, 15, 10)",
              100: "rgb(60, 20, 15)",
              200: "rgb(100, 30, 20)",
              300: "rgb(140, 45, 30)",
              400: "rgb(190, 60, 40)",
              500: "rgb(255, 154, 107)", // DEFAULT
              600: "rgb(255, 108, 52)",
              700: "rgb(207, 58, 0)",
              800: "rgb(180, 50, 0)",
              900: "rgb(140, 40, 0)",
            },
            default: {
              DEFAULT: "rgb(211, 211, 211)",
              50: "rgb(20, 20, 20)",
              100: "rgb(40, 40, 40)",
              200: "rgb(60, 60, 60)",
              300: "rgb(80, 80, 80)",
              400: "rgb(104, 104, 104)",
              500: "rgb(148, 148, 148)",
              600: "rgb(180, 180, 180)",
              700: "rgb(200, 200, 200)",
              800: "rgb(220, 220, 220)",
              900: "rgb(245, 245, 245)",
            },
          },
        },
      },
    }),
  ],
}
