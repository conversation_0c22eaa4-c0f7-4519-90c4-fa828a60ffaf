const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkSubscriptions() {
  try {
    const activeSubscriptions = await prisma.subscription.findMany({
      where: {
        status: 'ACTIVE',
      },
      include: {
        user: {
          select: {
            email: true,
            name: true,
          },
        },
        plan: {
          select: {
            name: true,
          },
        },
      },
      take: 5,
    })

    console.log('📊 Active Subscriptions:')
    if (activeSubscriptions.length === 0) {
      console.log('❌ No active subscriptions found')
    } else {
      activeSubscriptions.forEach((sub, index) => {
        console.log(`${index + 1}. ${sub.user.email} - ${sub.plan.name} (${sub.status})`)
      })
    }

    // Also check for any forms
    const forms = await prisma.form.findMany({
      where: {
        type: 'SUBSCRIPTION_CANCELLATION',
      },
      select: {
        id: true,
        title: true,
        status: true,
        isActive: true,
        _count: {
          select: {
            questions: true,
          },
        },
      },
    })

    console.log('\n📝 Cancellation Forms:')
    if (forms.length === 0) {
      console.log('❌ No cancellation forms found')
    } else {
      forms.forEach((form, index) => {
        console.log(`${index + 1}. ${form.title} - ${form.status} (Active: ${form.isActive}) - ${form._count.questions} questions`)
      })
    }

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkSubscriptions()
