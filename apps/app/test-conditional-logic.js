// Test script to create a form with conditional logic
const { PrismaClient } = require("@prisma/client")

const prisma = new PrismaClient()

async function createConditionalForm() {
  try {
    // Get the first user
    const firstUser = await prisma.user.findFirst()
    if (!firstUser) {
      console.error("❌ No users found in database.")
      return
    }

    // Check if there's already an active form and delete it
    const existingActiveForm = await prisma.form.findFirst({
      where: {
        type: "SUBSCRIPTION_CANCELLATION",
        isActive: true,
      },
    })

    if (existingActiveForm) {
      // Delete the existing form and its questions
      await prisma.formQuestion.deleteMany({
        where: { formId: existingActiveForm.id },
      })
      await prisma.form.delete({
        where: { id: existingActiveForm.id },
      })
      console.log("🗑️ Deleted existing active form")
    }

    // Create a new form with conditional logic
    const form = await prisma.form.create({
      data: {
        title: "Formulaire avec logique conditionnelle",
        description: "Test de la logique conditionnelle dans les formulaires.",
        type: "SUBSCRIPTION_CANCELLATION",
        status: "ACTIVE",
        isActive: true,
        showProgressBar: true,
        allowSaveProgress: false,
        requireAuth: true,
        enableConditionalLogic: true,
        createdBy: firstUser.id,
      },
    })

    // Create questions with conditional logic
    const questions = [
      {
        title: "Êtes-vous satisfait de notre service ?",
        type: "YES_NO",
        order: 0,
        isRequired: true,
        formId: form.id,
        showConditions: null, // Always show
      },
      {
        title: "Qu'est-ce qui vous a déplu le plus ?",
        description: 'Cette question n\'apparaît que si vous avez répondu "Non" à la question précédente.',
        type: "SINGLE_CHOICE",
        order: 1,
        isRequired: true,
        formId: form.id,
        options: JSON.stringify([
          { id: "price", label: "Prix trop élevé", value: "price" },
          { id: "features", label: "Fonctionnalités insuffisantes", value: "features" },
          { id: "support", label: "Support client", value: "support" },
          { id: "other", label: "Autre", value: "other" },
        ]),
        showConditions: JSON.stringify({
          groups: [
            {
              id: "group1",
              rules: [
                {
                  id: "rule1",
                  questionId: "", // Will be set after first question is created
                  operator: "equals",
                  value: false,
                },
              ],
              logic: "AND",
            },
          ],
          groupLogic: "AND",
        }),
      },
      {
        title: "Que pourrions-nous améliorer ?",
        description: "Cette question apparaît pour tous ceux qui ne sont pas satisfaits.",
        type: "TEXT_LONG",
        order: 2,
        isRequired: false,
        formId: form.id,
        maxLength: 500,
        showConditions: JSON.stringify({
          groups: [
            {
              id: "group1",
              rules: [
                {
                  id: "rule1",
                  questionId: "", // Will be set after first question is created
                  operator: "equals",
                  value: false,
                },
              ],
              logic: "AND",
            },
          ],
          groupLogic: "AND",
        }),
      },
      {
        title: "Recommanderiez-vous notre service ?",
        description: "Cette question n'apparaît que si vous êtes satisfait.",
        type: "RATING",
        order: 3,
        isRequired: true,
        formId: form.id,
        minValue: 1,
        maxValue: 5,
        showConditions: JSON.stringify({
          groups: [
            {
              id: "group1",
              rules: [
                {
                  id: "rule1",
                  questionId: "", // Will be set after first question is created
                  operator: "equals",
                  value: true,
                },
              ],
              logic: "AND",
            },
          ],
          groupLogic: "AND",
        }),
      },
      {
        title: "Commentaires finaux",
        description: "Toujours affiché - partagez vos derniers commentaires.",
        type: "TEXT_LONG",
        order: 4,
        isRequired: false,
        formId: form.id,
        maxLength: 300,
        showConditions: null, // Always show
      },
    ]

    // Create the first question (always visible)
    const firstQuestion = await prisma.formQuestion.create({
      data: questions[0],
    })

    // Update the conditional questions to reference the first question
    const conditionalQuestions = questions.slice(1).map((q) => {
      if (!q.showConditions) return q

      // Parse the JSON, update the questionId, and stringify again
      const conditions = JSON.parse(q.showConditions)
      conditions.groups.forEach((group) => {
        group.rules.forEach((rule) => {
          if (rule.questionId === "") {
            rule.questionId = firstQuestion.id
          }
        })
      })

      return {
        ...q,
        showConditions: JSON.stringify(conditions),
      }
    })

    // Create the remaining questions
    await prisma.formQuestion.createMany({
      data: conditionalQuestions,
    })

    console.log("✅ Conditional logic form created successfully!")
    console.log("Form ID:", form.id)
    console.log("Questions created:", questions.length)
    console.log("")
    console.log("📋 Test Scenarios:")
    console.log('1. Answer "Yes" to satisfaction → Should show recommendation rating + final comments')
    console.log('2. Answer "No" to satisfaction → Should show dissatisfaction reason + improvements + final comments')
    console.log("")
    console.log("🔗 Test the form at: http://localhost:3001/test-form")
  } catch (error) {
    console.error("❌ Error creating conditional form:", error)
  } finally {
    await prisma.$disconnect()
  }
}

createConditionalForm()
