{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "outDir": "dist", "rootDir": "src", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules"], "tsc-alias": {"resolveFullPaths": true}}