import { MangopayErrorResponse, MangopayRecurringPayIn, MangopayRecurringPayinRegistration } from "@/types/mangopay"
import { logger } from "@coheadcoaching/lib"

import { env } from "./env"

export function isMangopayError(data: unknown): data is MangopayErrorResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    "Type" in data &&
    typeof (data as MangopayErrorResponse).Type === "string" &&
    "errors" in data
  )
}

export function handleMangopayResponse<T>(data: unknown): T {
  if (isMangopayError(data)) {
    logger.error("MangoPay error response", { errorResponse: data })

    let errorMessage = data.Message || "Une erreur s'est produite avec MangoPay"
    if (data.errors) {
      errorMessage += `: ${Object.keys(data.errors)
        .map((key) => `${key}: ${data.errors?.[key]}`)
        .join("; ")}`
    }

    throw new Error(errorMessage)
  }

  return data as T
}

export async function getRecurringRegistrationDetails(
  registrationId: string
): Promise<MangopayRecurringPayinRegistration> {
  const myHeaders = new Headers()
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const options = { method: "GET", headers: myHeaders }

  const url = `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/viewRecurringPayment/${registrationId}`

  logger.log("Fetching Recurring Registration details", { url })

  const response = await fetch(url, options)

  if (!response.ok) {
    logger.error("Failed to fetch Recurring Registration details", {
      status: response.status,
      statusText: response.statusText,
      registrationId: registrationId,
    })

    throw new Error("Impossible de récupérer les détails de l'enregistrement de paiement récurrent")
  }

  const result = handleMangopayResponse<MangopayRecurringPayinRegistration>(await response.json())
  logger.log("Recurring Registration details fetched successfully", {
    registrationId: registrationId,
    status: result.Status,
  })

  return result
}

interface ProcessSubsequentRecurringPaymentParams {
  recurringPayinRegistrationId: string
  tag: string
  amount: number
  currency: string
  statementDescriptor: string
}

export async function processSubsequentRecurringPayment(
  params: ProcessSubsequentRecurringPaymentParams
): Promise<MangopayRecurringPayIn> {
  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)
  myHeaders.append(
    "Idempotency-Key",
    `mit_payment_${params.recurringPayinRegistrationId}_${new Date().toISOString().slice(0, 10)}_${Date.now()}`
  )

  const paymentPayload = JSON.stringify({
    RecurringPayinRegistrationId: params.recurringPayinRegistrationId,
    Tag: params.tag,
    DebitedFunds: {
      Currency: params.currency,
      Amount: params.amount,
    },
    Fees: {
      Currency: params.currency,
      Amount: 0,
    },
    StatementDescriptor: params.statementDescriptor,
  })

  const options = {
    method: "POST",
    headers: myHeaders,
    body: paymentPayload,
  }

  const url = `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/createRecurringPayInRegistrationMIT`

  logger.log("Processing subsequent recurring payment (MIT)", {
    url,
    registrationId: params.recurringPayinRegistrationId,
  })

  const response = await fetch(url, options)

  if (!response.ok) {
    const errorBody = await response.text().catch(() => "Could not read error body")
    logger.error("Failed to process subsequent recurring payment (MIT)", {
      status: response.status,
      statusText: response.statusText,
      registrationId: params.recurringPayinRegistrationId,
      errorBody: errorBody,
    })
    try {
      const errorJson = JSON.parse(errorBody)
      handleMangopayResponse(errorJson)
    } catch (e) {
      throw new Error(`Impossible de traiter le paiement récurrent suivant (MIT). Status: ${response.status}`)
    }
    throw new Error(`Impossible de traiter le paiement récurrent suivant (MIT). Status: ${response.status}`)
  }

  const result = handleMangopayResponse<MangopayRecurringPayIn>(await response.json())
  logger.log("Subsequent recurring payment (MIT) processed", {
    payinId: result.Id,
    status: result.Status,
    registrationId: params.recurringPayinRegistrationId,
  })

  return result
}
