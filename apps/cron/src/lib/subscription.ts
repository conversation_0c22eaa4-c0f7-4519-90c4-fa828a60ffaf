import { addMonths, addYears, startOfDay } from "date-fns"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, Payment, PaymentStatus, SubscriptionStatus } from "@prisma/client"

import { getRecurringRegistrationDetails, processSubsequentRecurringPayment } from "./mangopay"
import { prisma } from "./prisma"

export async function processDueSubscriptions() {
  const today = startOfDay(new Date())
  logger.log(`Starting subscription renewal process for date: ${today.toISOString()}`)

  const dueSubscriptions = await prisma.subscription.findMany({
    where: {
      status: SubscriptionStatus.ACTIVE,
      endDate: {
        lte: today,
      },
      mangopayRecurringRegistrationId: {
        not: null,
      },
    },
    include: {
      user: {
        select: { id: true, mangopayUserId: true, email: true },
      },
      plan: true,
    },
  })

  logger.log(`Found ${dueSubscriptions.length} subscriptions due for renewal.`)

  for (const subscription of dueSubscriptions) {
    logger.log(`Processing renewal for subscription: ${subscription.id}, User: ${subscription.userId}`)

    if (!subscription.mangopayRecurringRegistrationId || !subscription.user.mangopayUserId) {
      logger.error(`Subscription ${subscription.id} missing required Mangopay IDs. Skipping.`)
      await updateSubscriptionStatus(subscription.id, SubscriptionStatus.FAILED, "Configuration Mangopay manquante.")
      continue
    }

    try {
      const registrationDetails = await getRecurringRegistrationDetails(subscription.mangopayRecurringRegistrationId)
      if (registrationDetails.Status === "AUTHENTICATION_NEEDED") {
        logger.warn(
          `Subscription ${subscription.id} requires re-authentication (Registration Status: AUTHENTICATION_NEEDED). Skipping automatic renewal. Status set to PENDING.`
        )

        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.PENDING,
          "Re-authentification bancaire requise."
        )
        // TODO: Notify user about required action
        continue
      } else if (registrationDetails.Status === "ENDED") {
        logger.warn(
          `Subscription ${subscription.id} linked to an ENDED/FAILED registration (${registrationDetails.Status}). Skipping renewal. Status set to CANCELED/FAILED.`
        )
        const finalStatus =
          registrationDetails.Status === "ENDED" ? SubscriptionStatus.CANCELED : SubscriptionStatus.FAILED
        await updateSubscriptionStatus(
          subscription.id,
          finalStatus,
          `Enregistrement Mangopay terminé ou échoué (${registrationDetails.Status}).`
        )
        continue
      } else if (registrationDetails.Status !== "IN_PROGRESS" && registrationDetails.Status !== "CREATED") {
        logger.warn(
          `Subscription ${subscription.id} linked to registration with unexpected status: ${registrationDetails.Status}. Skipping renewal.`
        )
        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.FAILED,
          `Statut Mangopay inattendu: ${registrationDetails.Status}.`
        )
        continue
      }
    } catch (error) {
      logger.error(
        `Failed to get registration details for ${subscription.mangopayRecurringRegistrationId} during renewal check for sub ${subscription.id}: ${error instanceof Error ? error.message : String(error)}`,
        { error }
      )
      await updateSubscriptionStatus(subscription.id, SubscriptionStatus.FAILED, "Erreur vérification Mangopay.")
      continue
    }

    const amountInEuros =
      subscription.billingPeriod === BillingPeriod.MONTHLY
        ? subscription.plan.monthlyPrice / 100
        : subscription.plan.annualPrice / 100
    const amountInCents = Math.round(amountInEuros * 100)
    const currency = "EUR"

    let paymentRecord: Payment | null = null

    try {
      paymentRecord = await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount: amountInEuros,
          currency: currency,
          status: PaymentStatus.PENDING,
        },
      })
      logger.log(`Created pending payment record ${paymentRecord.id} for subscription ${subscription.id}`)

      const mitResult = await processSubsequentRecurringPayment({
        recurringPayinRegistrationId: subscription.mangopayRecurringRegistrationId,
        amount: amountInCents,
        currency: currency,
        tag: `CoheadCoaching Renewal - Sub: ${subscription.id}, Payment: ${paymentRecord.id}`,
        statementDescriptor: `ABO ${subscription.plan.name.substring(0, 6)}`,
      })

      if (mitResult.Status === "SUCCEEDED") {
        logger.log(`MIT Payment SUCCEEDED for subscription ${subscription.id}. PayIn ID: ${mitResult.Id}`)
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.SUCCEEDED,
            mangopayPayinId: mitResult.Id,
            failureReason: null,
          },
        })

        const newEndDate =
          subscription.billingPeriod === BillingPeriod.MONTHLY
            ? addMonths(subscription.endDate, 1)
            : addYears(subscription.endDate, 1)

        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.ACTIVE,
            endDate: newEndDate,
          },
        })
        logger.log(`Subscription ${subscription.id} renewed. New end date: ${newEndDate.toISOString()}`)
      } else {
        logger.error(
          `MIT Payment FAILED for subscription ${subscription.id}. Status: ${mitResult.Status}, Reason: ${mitResult.ResultMessage}`
        )
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.FAILED,
            mangopayPayinId: mitResult.Id,
            failureReason: mitResult.ResultMessage || "Paiement récurrent échoué",
          },
        })

        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.FAILED,
          `Échec du paiement récurrent : ${mitResult.ResultMessage}`
        )
        // TODO: Notify user about payment failure
      }
    } catch (error) {
      logger.error(
        `Error processing renewal for subscription ${subscription.id}: ${error instanceof Error ? error.message : String(error)}`,
        { error }
      )

      if (paymentRecord) {
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.FAILED,
            failureReason: `Erreur système lors du renouvellement : ${error instanceof Error ? error.message : String(error)}`,
          },
        })
      }
      await updateSubscriptionStatus(
        subscription.id,
        SubscriptionStatus.FAILED,
        `Erreur système lors du renouvellement.`
      )
      // TODO: Notify admin/monitor this error
    }
  }

  logger.log("Finished subscription renewal process.")
}

async function updateSubscriptionStatus(subscriptionId: string, status: SubscriptionStatus, logReason: string) {
  try {
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: { status: status, ...(status === SubscriptionStatus.CANCELED && { canceledAt: new Date() }) },
    })
    logger.log(`Subscription ${subscriptionId} status updated to ${status}. Reason: ${logReason}`)
  } catch (dbError) {
    logger.error(
      `Failed to update subscription ${subscriptionId} status to ${status} in DB: ${dbError instanceof Error ? dbError.message : String(dbError)}`,
      {
        dbError,
      }
    )
  }
}
