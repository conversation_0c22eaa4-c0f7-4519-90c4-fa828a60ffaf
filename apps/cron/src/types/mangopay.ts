type CurrencyAmount = {
  Currency: string
  Amount: number
}
type Address = {
  AddressLine1: string
  AddressLine2?: string
  City: string
  Region?: string
  PostalCode: string
  Country: string
}

type Contact = {
  FirstName: string
  LastName: string
  Address: Address
}

type BrowserInfo = {
  AcceptHeader: string
  JavaEnabled?: boolean
  JavascriptEnabled?: boolean
  Language: string
  ColorDepth: number
  ScreenHeight: number
  ScreenWidth: number
  TimeZoneOffset: number
  UserAgent: string
  IpAddress: string
}

type CardInfo = {
  BIN: string
  IssuingBank: string
  IssuerCountryCode: string
  Type: "DEBIT" | "CREDIT" | "CHARGE CARD"
  Brand: string
  SubType: string
}

type SecurityInfo = { AVSResult: string }

type CurrentState = {
  PayinsLinked: number
  CumulatedDebitedAmount: CurrencyAmount
  CumulatedFeesAmount: CurrencyAmount
  LastPayinId: string | null
}

export type MangopayErrorResponse = {
  Message: string
  Type: string
  Id: string
  Date: number
  errors: Record<string, string> | null
}

export type MangopayRecurringPayinRegistration = {
  Id: string
  Status: "CREATED" | "AUTHENTICATION_NEEDED" | "IN_PROGRESS" | "ENDED"
  ResultCode: string | null
  ResultMessage: string | null
  CurrentState: CurrentState
  RecurringType: "CLASSIC_SUBSCRIPTION" | "FRACTIONED_PAYMENT" | "CUSTOM"
  TotalAmount: CurrencyAmount | null
  CycleNumber: number | null
  AuthorId: string
  CardId: string
  CreditedUserId: string
  CreditedWalletId: string
  Billing: Contact
  Shipping: Contact
  EndDate: number | null // timestamp in seconds
  Frequency:
    | "Daily"
    | "Weekly"
    | "TwiceAMonth"
    | "Monthly"
    | "Bimonthly"
    | "Quarterly"
    | "Semiannual"
    | "Annual"
    | "Biannual"
  FixedNextAmount: boolean
  FractionedPayment: boolean
  FreeCycles: number
  FirstTransactionDebitedFunds: CurrencyAmount
  FirstTransactionFees: CurrencyAmount
  NextTransactionDebitedFunds: CurrencyAmount | null
  NextTransactionFees: CurrencyAmount | null
  Migration: false // deprecated, always false
  PaymentType: "CARD_DIRECT" | "PAYPAL"
}

export type MangopayRecurringPayIn = {
  Id: string
  Tag?: string
  CreationDate: number // timestamp
  AuthorId: string
  CreditedUserId: string
  DebitedFunds: CurrencyAmount
  CreditedFunds: CurrencyAmount
  Fees: CurrencyAmount
  Status: "CREATED" | "SUCCEEDED" | "FAILED"
  ResultCode: string
  ResultMessage: string
  ExecutionDate: number | null
  Type: "PAYIN" | "TRANSFER" | "CONVERSION" | "PAYOUT"
  Nature: "REGULAR" | "REPUDIATION" | "REFUND" | "SETTLEMENT"
  CreditedWalletId: string
  DebitedWalletId: string | null
  PaymentType: "CARD" | "DIRECT_DEBIT" | "PREAUTHORIZED" | "BANK_WIRE"
  ExecutionType: "WEB" | "DIRECT" | "EXTERNAL_INSTRUCTION"
  SecureMode: "DEFAULT" | "FORCE" | "NO_CHOICE"
  CardId: string
  SecureModeReturnURL?: string
  SecureModeRedirectURL?: string
  SecureModeNeeded: boolean
  Culture?: string
  SecurityInfo?: SecurityInfo
  StatementDescriptor?: string
  BrowserInfo?: BrowserInfo
  Billing?: Contact
  Shipping?: Contact
  Requested3DSVersion?: "V1" | "V2_1"
  Applied3DSVersion?: "V1" | "V2_1"
  RecurringPayinRegistrationId: string
  PreferredCardNetwork?: "VISA" | "MASTERCARD" | "CB" | "MAESTRO"
  CardInfo?: CardInfo | null
}
