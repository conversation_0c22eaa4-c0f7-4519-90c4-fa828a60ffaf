import { exit } from "process"

import { processDueSubscriptions } from "@/lib/subscription"
import { logger } from "@coheadcoaching/lib"

const processSubscriptions = async () => {
  await processDueSubscriptions()
}

const main = async () => {
  const maxDurationWarning = 1000 * 60 * 5 // 5 minutes
  const name = "ProcessSubscriptions"
  const now = new Date()

  logger.prefix = () => `[${new Date().toLocaleString()}] `
  await processSubscriptions().catch((err) => {
    logger.error(
      `${name} started at ${now.toLocaleString()} and failed after ${new Date().getTime() - now.getTime()}ms`
    )
    throw err
  })
  const took = new Date().getTime() - now.getTime()
  if (took > maxDurationWarning) logger.warn(`${name} took ${took}ms`)

  exit(0)
}

main()
