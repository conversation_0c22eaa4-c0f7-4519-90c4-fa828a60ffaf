name: coheadcoaching
services:
  db:
    image: postgres:latest
    container_name: cv-ai_db
    restart: unless-stopped
    volumes:
      - coheadcoaching-postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    logging:
      driver: json-file
      options:
        max-size: 50m
    ports:
      - 5432:5432
  redis:
    image: redis:latest
    restart: unless-stopped
    container_name: cv-ai_redis
    command: /bin/sh -c "redis-server"
    logging:
      driver: json-file
      options:
        max-size: 50m
    volumes:
      - coheadcoaching-redis-data:/data
    ports:
      - 6379:6379
volumes:
  coheadcoaching-postgres-data: null
  coheadcoaching-redis-data: null
