# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.development.local
.env.test.local
.env.production.local
.env
/test-results/
/playwright-report/
/playwright/.cache/

.turbo
build
dist
.next

output.json
*.terraform*
!.terraform.lock.hcl
!terraform.tfstate
*.tfstate.backup
.auto.tfvars.json