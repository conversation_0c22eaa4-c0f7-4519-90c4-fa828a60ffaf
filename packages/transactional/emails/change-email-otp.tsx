import * as React from "react"

import { CodeInline, Container as <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"
import { muted } from "../constants"

interface ChangeEmailProps {
  previewText: string
  code: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  heyText: string
}

export const ChangeEmail = ({
  previewText,
  code,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  heyText,
}: ChangeEmailProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>
          <OContainer style={codeContainerStyle}>
            <CodeInline>{code}</CodeInline>
          </OContainer>
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: ChangeEmailProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Confirmez votre nouvel e-mail",
  supportEmail: "<EMAIL>",
  code: "123456",
  titleText: "Confirmez votre nouvel e-mail",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de compte. Si vous avez des questions, veuillez nous contacter à",
  contentTitle:
    "Vous avez demandé à changer votre adresse e-mail. Veuillez utiliser le code suivant pour valider votre demande.",
  heyText: "Hey",
}
ChangeEmail.PreviewProps = previewProps

export default ChangeEmail

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const

const codeContainerStyle = {
  backgroundColor: muted,
  borderRadius: "12px",
  fontSize: "22px",
  padding: "10px",
  width: "max-content",
  textAlign: "center",
  margin: "1rem auto 0",
} as const
