import * as React from "react"

import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Button } from "../components/button"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface PaymentFailedProps {
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  heyText: string
  amount: number
  currency: string
  date: Date
  failureReason: string
  retryUrl?: string
}

export const PaymentFailed = ({
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  heyText,
  amount,
  currency,
  date,
  failureReason,
  retryUrl,
}: PaymentFailedProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>

          <Text style={paymentDetails}>
            <strong>Amount:</strong> {amount.toFixed(2)} {currency}
            <br />
            <strong>Date:</strong> {date.toLocaleDateString()}
            <br />
            <strong>Status:</strong> Failed
            <br />
            <strong>Reason:</strong> {failureReason}
          </Text>

          {retryUrl && <Button href={retryUrl}>Retry Payment</Button>}
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: PaymentFailedProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Votre paiement a échoué",
  supportEmail: "<EMAIL>",
  titleText: "Échec du paiement",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de paiement. Si vous avez des questions, veuillez nous contacter à",
  contentTitle: "Nous n'avons pas pu traiter votre paiement. Voici les détails :",
  heyText: "Hey",
  amount: 99.99,
  currency: "EUR",
  date: new Date(),
  failureReason: "Fonds insuffisants",
  retryUrl: "https://coheadcoaching.com/payment/retry/abc123",
}

PaymentFailed.PreviewProps = previewProps

export default PaymentFailed

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const

const paymentDetails = {
  margin: "15px 0",
  lineHeight: "1.5",
  textAlign: "left",
} as const
