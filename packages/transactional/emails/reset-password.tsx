import * as React from "react"

import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Button } from "../components/button"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface ResetPasswordProps {
  resetLink: string
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  actionText: string
  heyText: string
}

export const ResetPassword = ({
  resetLink,
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  actionText,
  heyText,
}: ResetPasswordProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>
          <Button href={resetLink}>{actionText}</Button>
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: ResetPasswordProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Demande de réinitialisation du mot de passe",
  supportEmail: "<EMAIL>",
  resetLink: "https://coheadcoaching.com/reset-password?token=abc123",
  titleText: "Réinitialisez votre mot de passe",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de compte. Si vous avez des questions, veuillez nous contacter à",
  contentTitle:
    "Vous avez récemment demandé à réinitialiser votre mot de passe pour votre compte. Cliquez sur le bouton ci-dessous pour le réinitialiser.",
  actionText: "Réinitialiser le mot de passe",
  heyText: "Hey",
}
ResetPassword.PreviewProps = previewProps

export default ResetPassword

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const
