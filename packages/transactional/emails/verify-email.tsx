import * as React from "react"

import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Button } from "../components/button"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface VerifyEmailProps {
  verificationLink: string
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  actionText: string
  heyText: string
}

export const VerifyEmail = ({
  verificationLink,
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  actionText,
  heyText,
}: VerifyEmailProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>
          <Button href={verificationLink}>{actionText}</Button>
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: VerifyEmailProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Vérifiez votre adresse e-mail pour compléter votre inscription.",
  supportEmail: "<EMAIL>",
  verificationLink: "https://coheadcoaching.com/verify-email?token=abc123",
  titleText: "Vérifiez votre adresse e-mail",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de compte. Si vous avez des questions, veuillez nous contacter à",
  contentTitle:
    "Merci de vous être inscrit à CoheadCoaching. Pour compléter votre inscription, nous devons juste vérifier votre adresse e-mail.",
  actionText: "Verify email",
  heyText: "Hey",
}
VerifyEmail.PreviewProps = previewProps

export default VerifyEmail

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const
