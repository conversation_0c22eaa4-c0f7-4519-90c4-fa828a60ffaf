{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coheadcoaching/tsconfig/next.json", "include": ["."], "exclude": ["dist", "build", "node_modules"], "compilerOptions": {"strict": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "esModuleInterop": true, "incremental": false, "isolatedModules": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "ES2022"}}