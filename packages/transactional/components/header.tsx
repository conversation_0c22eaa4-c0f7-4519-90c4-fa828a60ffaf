import React from "react"

import { Container, Img, Text } from "@react-email/components"

import { primary } from "../constants"

export const Header = ({ logoUrl, titleText }: { logoUrl: string; titleText: string }) => {
  return (
    <Container style={headerContainer}>
      <Img src={logoUrl} width="120" height="auto" alt="CoheadCoaching" style={logoStyle} />
      <Text style={title}>{titleText}</Text>
    </Container>
  )
}

const headerContainer = {
  textAlign: "center",
  marginBottom: "24px",
} as const

const logoStyle = {
  margin: "0 auto",
  marginBottom: "16px",
} as const

const title = {
  fontSize: "24px",
  lineHeight: 1.25,
  fontWeight: "bold",
  color: primary,
  textAlign: "center",
  margin: "0",
} as const
