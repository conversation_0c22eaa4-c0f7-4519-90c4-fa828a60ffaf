import React, { ReactNode } from "react"

import { Button as OButton, ButtonProps } from "@react-email/components"

import { buttonPrimary } from "../constants"

export const Button = ({
  children,
  ...props
}: {
  children: ReactNode
} & ButtonProps) => {
  return (
    <OButton
      {...props}
      style={{
        ...buttonStyle,
        ...props.style,
      }}
    >
      {children}
    </OButton>
  )
}

const buttonStyle = {
  fontSize: "1rem",
  backgroundColor: buttonPrimary,
  color: "#FFFFFF", // Texte blanc pour meilleur contraste
  lineHeight: "1.5rem",
  borderRadius: "8px",
  padding: "12px 24px",
  minWidth: "8rem",
  fontWeight: "bold",
  textAlign: "center",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  border: "none",
  cursor: "pointer",
} as const
