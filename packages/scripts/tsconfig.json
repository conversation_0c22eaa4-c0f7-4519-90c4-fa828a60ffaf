{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": "."}, "include": ["**/*.ts"], "exclude": ["node_modules"]}