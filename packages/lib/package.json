{"name": "@coheadcoaching/lib", "version": "0.0.0", "main": "./dist/cjs/index.cjs", "module": "./dist/esm/index.mjs", "types": "./dist/cjs/index.d.cts", "scripts": {"build": "pkgroll", "type-check": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint --fix .", "prettier": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.mts", "default": "./dist/esm/index.mjs"}}, "dependencies": {"chalk": "^5.3.0", "custom-prettier-config": "*", "eslint-config-custom": "*", "ora": "^8.0.1", "typescript": "^5.4.3", "pkgroll": "^2.1.1"}, "type": "module", "browser": {"child_process": false, "readline": false, "ora": false}}